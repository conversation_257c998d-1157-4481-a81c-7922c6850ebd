#pragma once

#include "../core/protection_engine.h"
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>

// Forward declarations
struct ImGuiContext;
struct ImDrawData;

// UI Theme
enum class UITheme {
    DARK_CYBER,
    DARK_PROFESSIONAL,
    LIGHT_PROFESSIONAL,
    HACKER_GREEN,
    SECURITY_BLUE,
    CUSTOM
};

// UI Colors
struct UIColors {
    float background[4] = {0.06f, 0.06f, 0.08f, 1.0f};
    float windowBg[4] = {0.08f, 0.08f, 0.10f, 1.0f};
    float header[4] = {0.12f, 0.12f, 0.15f, 1.0f};
    float headerHovered[4] = {0.15f, 0.15f, 0.18f, 1.0f};
    float headerActive[4] = {0.18f, 0.18f, 0.22f, 1.0f};
    float button[4] = {0.20f, 0.25f, 0.30f, 1.0f};
    float buttonHovered[4] = {0.25f, 0.30f, 0.35f, 1.0f};
    float buttonActive[4] = {0.30f, 0.35f, 0.40f, 1.0f};
    float text[4] = {0.95f, 0.95f, 0.95f, 1.0f};
    float textDisabled[4] = {0.50f, 0.50f, 0.50f, 1.0f};
    float accent[4] = {0.26f, 0.59f, 0.98f, 1.0f};
    float success[4] = {0.20f, 0.80f, 0.20f, 1.0f};
    float warning[4] = {1.0f, 0.65f, 0.0f, 1.0f};
    float error[4] = {0.90f, 0.20f, 0.20f, 1.0f};
    float critical[4] = {1.0f, 0.0f, 0.0f, 1.0f};
};

// UI Configuration
struct UIConfig {
    UITheme theme = UITheme::DARK_CYBER;
    UIColors colors;
    float fontSize = 16.0f;
    float iconSize = 20.0f;
    float windowRounding = 8.0f;
    float frameRounding = 4.0f;
    std::string customFontPath;
    float scrollbarRounding = 12.0f;
    float grabRounding = 3.0f;
    float tabRounding = 4.0f;
    bool enableAnimations = true;
    bool enableSounds = false;
    bool enableNotifications = true;
    bool enableTooltips = true;
    bool enableStatusBar = true;
    bool enableMenuBar = true;
    bool enableDocking = true;
    bool enableViewports = false;
    float animationSpeed = 1.0f;
    int maxLogEntries = 10000;
    int refreshRate = 60;
};

// Window state
struct WindowState {
    bool showMainWindow = true;
    bool showDashboard = true;
    bool showThreatMonitor = true;
    bool showProcessMonitor = true;
    bool showNetworkMonitor = false;
    bool showConfiguration = false;
    bool showStatistics = false;
    bool showLogs = false;
    bool showAbout = false;
    bool showDemo = false;
    
    // Window positions and sizes
    float mainWindowPos[2] = {100.0f, 100.0f};
    float mainWindowSize[2] = {1200.0f, 800.0f};
    bool mainWindowMaximized = false;
};

// Chart data
struct ChartData {
    std::vector<float> values;
    std::vector<std::string> labels;
    float minValue = 0.0f;
    float maxValue = 100.0f;
    size_t maxPoints = 100;
    std::string title;
    std::string unit;
    float color[4] = {0.26f, 0.59f, 0.98f, 1.0f};
};

/**
 * @brief Professional Protection System UI
 * 
 * Advanced ImGui-based user interface for the protection system with
 * modern design, real-time monitoring, and comprehensive controls.
 */
class ProtectionUI {
public:
    ProtectionUI();
    ~ProtectionUI();

    // Initialization
    bool Initialize(HWND hwnd, ProtectionEngine* engine);
    void Shutdown();
    bool IsInitialized() const { return m_initialized; }

    // Main loop
    void NewFrame();
    void Render();
    void EndFrame();
    
    // Window management
    void Show();
    void Hide();
    bool IsVisible() const { return m_visible; }
    void SetVisible(bool visible) { m_visible = visible; }

    // Theme and styling
    void SetTheme(UITheme theme);
    void SetCustomColors(const UIColors& colors);
    void ApplyTheme();
    void LoadFonts();
    void SetFontSize(float size);

    // Configuration
    void SetConfig(const UIConfig& config) { m_config = config; ApplyConfig(); }
    const UIConfig& GetConfig() const { return m_config; }
    void SaveConfig(const std::string& filePath) const;
    bool LoadConfig(const std::string& filePath);

    // Event handling
    bool ProcessMessage(UINT msg, WPARAM wParam, LPARAM lParam);
    void SetCloseCallback(std::function<void()> callback) { m_closeCallback = callback; }

    // Data updates
    void UpdateThreatData();
    void UpdateProcessData();
    void UpdateNetworkData();
    void UpdateStatistics();

private:
    bool m_initialized;
    bool m_visible;
    HWND m_hwnd;
    ProtectionEngine* m_engine;
    
    // ImGui context
    ImGuiContext* m_context;
    
    // Configuration
    UIConfig m_config;
    WindowState m_windowState;

    // Fonts
    struct FontSet {
        void* regular = nullptr;
        void* bold = nullptr;
        void* large = nullptr;
        void* small = nullptr;
        void* icon = nullptr;
    } m_fonts;
    
    // Data
    std::vector<ThreatInfo> m_recentThreats;
    std::vector<ProcessInfo> m_monitoredProcesses;
    ProtectionEngine::Statistics m_statistics;
    
    // Charts and graphs
    ChartData m_threatChart;
    ChartData m_cpuChart;
    ChartData m_memoryChart;
    ChartData m_networkChart;
    
    // UI state
    int m_selectedThreat = -1;
    int m_selectedProcess = -1;
    std::string m_searchFilter;
    bool m_autoScroll = true;
    float m_lastUpdateTime = 0.0f;
    
    // Callbacks
    std::function<void()> m_closeCallback;
    
    // Rendering methods
    void RenderMainMenuBar();
    void RenderDashboard();
    void RenderThreatMonitor();
    void RenderProcessMonitor();
    void RenderNetworkMonitor();
    void RenderConfiguration();
    void RenderStatistics();
    void RenderLogs();
    void RenderAbout();
    
    // Widget helpers
    void RenderThreatTable();
    void RenderProcessTable();
    void RenderProtectionStatus();
    void RenderSystemInfo();
    void RenderChart(const ChartData& data, float width, float height);
    void RenderProgressRing(float progress, float radius, const float* color);
    void RenderStatusIndicator(const std::string& label, bool status, const std::string& tooltip = "");
    void RenderThreatSeverityBadge(ThreatSeverity severity);
    void RenderProtectionLevelBadge(ProtectionLevel level);
    
    // Utility methods
    void ApplyConfig();
    void UpdateChartData();
    void AddLogEntry(const std::string& message, const float* color = nullptr);
    std::string FormatFileSize(size_t bytes);
    std::string FormatDuration(DWORD seconds);
    std::string FormatTimestamp(const SYSTEMTIME& time);
    const float* GetThreatSeverityColor(ThreatSeverity severity);
    const float* GetProtectionLevelColor(ProtectionLevel level);
    
    // Icon helpers
    const char* GetThreatTypeIcon(ThreatType type);
    const char* GetProtectionStatusIcon(bool enabled);
    const char* GetProcessStatusIcon(bool suspicious);
    
    // Animation helpers
    float GetAnimationValue(const std::string& id, float target, float speed = 5.0f);
    void PushStyleAnimation(const std::string& id, float target, float speed = 5.0f);
    void PopStyleAnimation();
    
    // Layout helpers
    void BeginCenteredGroup(float width);
    void EndCenteredGroup();
    void SameLine(float spacing = -1.0f);
    void Spacing(int count = 1);
    void Separator();
    
    // Custom widgets
    bool ToggleButton(const char* label, bool* value, const float* color = nullptr);
    bool ColoredButton(const char* label, const float* color, const float* hoverColor = nullptr);
    bool IconButton(const char* icon, const char* tooltip = nullptr);
    void HelpMarker(const char* desc);
    void StatusText(const char* text, bool status);
    
    // Docking and layout
    void SetupDockSpace();
    void SaveLayout();
    void LoadLayout();
    
    // Notifications
    struct Notification {
        std::string title;
        std::string message;
        float color[4];
        float duration;
        float startTime;
        bool isVisible;
    };
    std::vector<Notification> m_notifications;
    void ShowNotification(const std::string& title, const std::string& message, 
                         const float* color = nullptr, float duration = 5.0f);
    void RenderNotifications();
    
    // Context menus
    void RenderThreatContextMenu(const ThreatInfo& threat);
    void RenderProcessContextMenu(const ProcessInfo& process);
    
    // Dialogs
    bool m_showConfigDialog = false;
    bool m_showAboutDialog = false;
    bool m_showThreatDetailsDialog = false;
    bool m_showProcessDetailsDialog = false;
    
    void RenderConfigDialog();
    void RenderAboutDialog();
    void RenderThreatDetailsDialog();
    void RenderProcessDetailsDialog();
    
    // Performance monitoring
    float m_frameTime = 0.0f;
    float m_fps = 0.0f;
    std::vector<float> m_frameTimes;
    void UpdatePerformanceMetrics();
    void RenderPerformanceOverlay();

    // Theme functions
    void ApplyCyberTheme();
    void ApplyProfessionalTheme();
    void ApplySecurityTheme();
};
