#include "protection_engine.h"
#include "hooking_engine.h"
#include "threat_detector.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <psapi.h>
#include <tlhelp32.h>
#include <winternl.h>

// Define missing constants
#ifndef ProcessDebugPort
#define ProcessDebugPort 7
#endif

#ifndef ProcessDebugFlags
#define ProcessDebugFlags 31
#endif

#ifndef ProcessDebugObjectHandle
#define ProcessDebugObjectHandle 30
#endif

ProtectionEngine::ProtectionEngine() {
    memset(&m_statistics, 0, sizeof(m_statistics));
    GetSystemTime(&m_startTime);
}

ProtectionEngine::~ProtectionEngine() {
    Shutdown();
}

bool ProtectionEngine::Initialize() {
    if (m_initialized) {
        return true;
    }

    try {
        std::cout << "[ProtectionEngine] Initializing Advanced Protection System..." << std::endl;

        // Initialize core components
        if (!InitializeComponents()) {
            std::cerr << "[ProtectionEngine] Failed to initialize components" << std::endl;
            return false;
        }

        // Set default configuration
        m_config.level = ProtectionLevel::MONITORING;
        m_config.enableAntiDebugging = true;
        m_config.enableProcessHollowingDetection = true;
        m_config.enableCodeInjectionDetection = true;
        m_config.enableAPIHookingDetection = true;
        m_config.enableMemoryProtection = true;
        m_config.enableNetworkMonitoring = true;
        m_config.enableBehaviorAnalysis = true;
        m_config.enableRealTimeScanning = true;
        m_config.blockThreats = false; // Start in monitoring mode
        m_config.logThreats = true;
        m_config.alertUser = true;

        m_initialized = true;
        std::cout << "[ProtectionEngine] Successfully initialized!" << std::endl;
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[ProtectionEngine] Initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void ProtectionEngine::Shutdown() {
    if (!m_initialized) {
        return;
    }

    std::cout << "[ProtectionEngine] Shutting down..." << std::endl;

    // Stop protection if active
    if (m_protectionActive) {
        StopProtection();
    }

    // Shutdown components
    ShutdownComponents();

    // Wait for worker threads to finish
    m_shouldStop = true;
    for (HANDLE thread : m_workerThreads) {
        if (thread) {
            WaitForSingleObject(thread, 5000);
            CloseHandle(thread);
        }
    }
    m_workerThreads.clear();

    m_initialized = false;
    std::cout << "[ProtectionEngine] Shutdown complete" << std::endl;
}

bool ProtectionEngine::InitializeComponents() {
    // Initialize Hooking Engine
    m_hookingEngine = std::make_unique<HookingEngine>();
    if (!m_hookingEngine->Initialize()) {
        std::cerr << "[ProtectionEngine] Failed to initialize hooking engine" << std::endl;
        return false;
    }

    // Initialize Threat Detector
    m_threatDetector = std::make_unique<ThreatDetector>();
    if (!m_threatDetector->Initialize()) {
        std::cerr << "[ProtectionEngine] Failed to initialize threat detector" << std::endl;
        return false;
    }

    // Set up callbacks
    m_threatDetector->SetThreatDetectedCallback(
        [this](const ThreatInfo& threat) {
            OnThreatDetected(threat);
        }
    );

    std::cout << "[ProtectionEngine] All components initialized successfully" << std::endl;
    return true;
}

void ProtectionEngine::ShutdownComponents() {
    if (m_threatDetector) {
        m_threatDetector->Shutdown();
        m_threatDetector.reset();
    }

    if (m_hookingEngine) {
        m_hookingEngine->Shutdown();
        m_hookingEngine.reset();
    }
}

bool ProtectionEngine::StartProtection() {
    if (!m_initialized) {
        std::cerr << "[ProtectionEngine] Engine not initialized" << std::endl;
        return false;
    }

    if (m_protectionActive) {
        std::cout << "[ProtectionEngine] Protection already active" << std::endl;
        return true;
    }

    std::cout << "[ProtectionEngine] Starting protection..." << std::endl;

    try {
        // Install core protection hooks based on configuration
        if (m_config.enableAntiDebugging) {
            InstallAntiDebuggingHooks();
        }

        if (m_config.enableProcessHollowingDetection) {
            InstallProcessHollowingHooks();
        }

        if (m_config.enableCodeInjectionDetection) {
            InstallCodeInjectionHooks();
        }

        if (m_config.enableAPIHookingDetection) {
            InstallAPIHookingDetectionHooks();
        }

        if (m_config.enableMemoryProtection) {
            InstallMemoryProtectionHooks();
        }

        if (m_config.enableNetworkMonitoring) {
            InstallNetworkMonitoringHooks();
        }

        // Start real-time threat detection
        if (m_config.enableRealTimeScanning && m_threatDetector) {
            m_threatDetector->StartRealTimeDetection();
        }

        // Start monitoring threads
        StartMonitoringThreads();

        m_protectionActive = true;
        UpdateStatistics();

        std::cout << "[ProtectionEngine] Protection started successfully!" << std::endl;
        std::cout << "[ProtectionEngine] Protection Level: " << ProtectionLevelToString(m_config.level) << std::endl;
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[ProtectionEngine] Failed to start protection: " << e.what() << std::endl;
        return false;
    }
}

bool ProtectionEngine::StopProtection() {
    if (!m_protectionActive) {
        std::cout << "[ProtectionEngine] Protection not active" << std::endl;
        return true;
    }

    std::cout << "[ProtectionEngine] Stopping protection..." << std::endl;

    try {
        // Stop real-time detection
        if (m_threatDetector) {
            m_threatDetector->StopRealTimeDetection();
        }

        // Remove all hooks
        if (m_hookingEngine) {
            m_hookingEngine->RemoveAllHooks();
        }

        // Stop monitoring threads
        m_shouldStop = true;

        m_protectionActive = false;
        UpdateStatistics();

        std::cout << "[ProtectionEngine] Protection stopped successfully" << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[ProtectionEngine] Failed to stop protection: " << e.what() << std::endl;
        return false;
    }
}

void ProtectionEngine::InstallAntiDebuggingHooks() {
    if (!m_hookingEngine) return;

    std::cout << "[ProtectionEngine] Installing anti-debugging hooks..." << std::endl;

    // Hook IsDebuggerPresent
    m_hookingEngine->InstallHook(
        "IsDebuggerPresent",
        "kernel32.dll",
        "IsDebuggerPresent",
        [this](const HookContext& context, void** parameters, size_t paramCount) -> bool {
            ThreatInfo threat;
            threat.type = ThreatType::DEBUGGER_DETECTION;
            threat.severity = ThreatSeverity::HIGH;
            threat.description = "IsDebuggerPresent() called - Debugger detection attempt";
            threat.processId = context.processId;
            threat.threadId = context.threadId;
            threat.address = context.returnAddress;
            GetSystemTime(&threat.timestamp);
            
            OnThreatDetected(threat);
            
            // Return false to bypass debugger detection
            if (m_config.level >= ProtectionLevel::ACTIVE) {
                *reinterpret_cast<BOOL*>(parameters[0]) = FALSE;
                return true; // Block original call
            }
            
            return false; // Allow original call
        }
    );

    // Hook CheckRemoteDebuggerPresent
    m_hookingEngine->InstallHook(
        "CheckRemoteDebuggerPresent",
        "kernel32.dll",
        "CheckRemoteDebuggerPresent",
        [this](const HookContext& context, void** parameters, size_t paramCount) -> bool {
            ThreatInfo threat;
            threat.type = ThreatType::DEBUGGER_DETECTION;
            threat.severity = ThreatSeverity::HIGH;
            threat.description = "CheckRemoteDebuggerPresent() called - Remote debugger detection attempt";
            threat.processId = context.processId;
            threat.threadId = context.threadId;
            threat.address = context.returnAddress;
            GetSystemTime(&threat.timestamp);
            
            OnThreatDetected(threat);
            
            // Set result to FALSE to bypass detection
            if (m_config.level >= ProtectionLevel::ACTIVE && paramCount >= 2) {
                *reinterpret_cast<PBOOL>(parameters[1]) = FALSE;
                *reinterpret_cast<BOOL*>(parameters[0]) = TRUE; // Success
                return true; // Block original call
            }
            
            return false; // Allow original call
        }
    );

    // Hook NtQueryInformationProcess
    m_hookingEngine->InstallHook(
        "NtQueryInformationProcess",
        "ntdll.dll",
        "NtQueryInformationProcess",
        [this](const HookContext& context, void** parameters, size_t paramCount) -> bool {
            if (paramCount >= 2) {
                PROCESSINFOCLASS infoClass = *reinterpret_cast<PROCESSINFOCLASS*>(parameters[1]);
                
                // Check for debugger-related queries
                if (infoClass == ProcessDebugPort || infoClass == ProcessDebugFlags || 
                    infoClass == ProcessDebugObjectHandle) {
                    
                    ThreatInfo threat;
                    threat.type = ThreatType::DEBUGGER_DETECTION;
                    threat.severity = ThreatSeverity::MEDIUM;
                    threat.description = "NtQueryInformationProcess() called with debugger-related info class";
                    threat.processId = context.processId;
                    threat.threadId = context.threadId;
                    threat.address = context.returnAddress;
                    GetSystemTime(&threat.timestamp);
                    
                    OnThreatDetected(threat);
                    
                    // Return fake information to bypass detection
                    if (m_config.level >= ProtectionLevel::ACTIVE) {
                        // Set appropriate fake values based on info class
                        return true; // Block original call
                    }
                }
            }
            
            return false; // Allow original call
        }
    );
}

void ProtectionEngine::InstallProcessHollowingHooks() {
    if (!m_hookingEngine) return;

    std::cout << "[ProtectionEngine] Installing process hollowing detection hooks..." << std::endl;

    // Hook NtUnmapViewOfSection
    m_hookingEngine->InstallHook(
        "NtUnmapViewOfSection",
        "ntdll.dll",
        "NtUnmapViewOfSection",
        [this](const HookContext& context, void** parameters, size_t paramCount) -> bool {
            ThreatInfo threat;
            threat.type = ThreatType::PROCESS_HOLLOWING;
            threat.severity = ThreatSeverity::CRITICAL;
            threat.description = "NtUnmapViewOfSection() called - Possible process hollowing attempt";
            threat.processId = context.processId;
            threat.threadId = context.threadId;
            threat.address = context.returnAddress;
            GetSystemTime(&threat.timestamp);
            
            OnThreatDetected(threat);
            
            // Block if in aggressive mode
            if (m_config.level >= ProtectionLevel::AGGRESSIVE && m_config.blockThreats) {
                return true; // Block the call
            }
            
            return false; // Allow original call
        }
    );

    // Hook NtMapViewOfSection
    m_hookingEngine->InstallHook(
        "NtMapViewOfSection",
        "ntdll.dll",
        "NtMapViewOfSection",
        [this](const HookContext& context, void** parameters, size_t paramCount) -> bool {
            // Analyze the mapping for suspicious patterns
            ThreatInfo threat;
            threat.type = ThreatType::PROCESS_HOLLOWING;
            threat.severity = ThreatSeverity::HIGH;
            threat.description = "NtMapViewOfSection() called - Monitoring for process hollowing";
            threat.processId = context.processId;
            threat.threadId = context.threadId;
            threat.address = context.returnAddress;
            GetSystemTime(&threat.timestamp);
            
            OnThreatDetected(threat);
            
            return false; // Allow original call but monitor
        }
    );
}

void ProtectionEngine::StartMonitoringThreads() {
    m_shouldStop = false;

    // Start main monitoring thread
    HANDLE monitoringThread = CreateThread(
        nullptr, 0, MonitoringThread, this, 0, nullptr
    );
    if (monitoringThread) {
        m_workerThreads.push_back(monitoringThread);
    }

    // Start analysis thread
    HANDLE analysisThread = CreateThread(
        nullptr, 0, AnalysisThread, this, 0, nullptr
    );
    if (analysisThread) {
        m_workerThreads.push_back(analysisThread);
    }
}

DWORD WINAPI ProtectionEngine::MonitoringThread(LPVOID param) {
    ProtectionEngine* engine = static_cast<ProtectionEngine*>(param);
    
    std::cout << "[ProtectionEngine] Monitoring thread started" << std::endl;
    
    while (!engine->m_shouldStop) {
        try {
            // Update process list
            engine->UpdateProcessList();
            
            // Update statistics
            engine->UpdateStatistics();
            
            // Sleep for a short interval
            Sleep(1000);
        }
        catch (const std::exception& e) {
            std::cerr << "[ProtectionEngine] Exception in monitoring thread: " << e.what() << std::endl;
        }
    }
    
    std::cout << "[ProtectionEngine] Monitoring thread stopped" << std::endl;
    return 0;
}

DWORD WINAPI ProtectionEngine::AnalysisThread(LPVOID param) {
    ProtectionEngine* engine = static_cast<ProtectionEngine*>(param);
    
    std::cout << "[ProtectionEngine] Analysis thread started" << std::endl;
    
    while (!engine->m_shouldStop) {
        try {
            // Perform periodic analysis
            engine->PerformPeriodicAnalysis();
            
            // Sleep for longer interval
            Sleep(5000);
        }
        catch (const std::exception& e) {
            std::cerr << "[ProtectionEngine] Exception in analysis thread: " << e.what() << std::endl;
        }
    }
    
    std::cout << "[ProtectionEngine] Analysis thread stopped" << std::endl;
    return 0;
}

void ProtectionEngine::OnThreatDetected(const ThreatInfo& threat) {
    {
        std::lock_guard<std::mutex> lock(m_threatsMutex);
        m_detectedThreats.push_back(threat);
        
        // Keep only recent threats (last 10000)
        if (m_detectedThreats.size() > 10000) {
            m_detectedThreats.erase(m_detectedThreats.begin());
        }
    }

    // Log the threat
    if (m_config.logThreats) {
        std::cout << "[THREAT] " << ThreatTypeToString(threat.type) 
                  << " - " << ThreatSeverityToString(threat.severity)
                  << " - " << threat.description << std::endl;
    }

    // Call user callback
    if (m_threatCallback) {
        m_threatCallback(threat);
    }

    UpdateStatistics();
}

std::string ProtectionEngine::ThreatTypeToString(ThreatType type) {
    switch (type) {
        case ThreatType::DEBUGGER_DETECTION: return "Debugger Detection";
        case ThreatType::PROCESS_HOLLOWING: return "Process Hollowing";
        case ThreatType::CODE_INJECTION: return "Code Injection";
        case ThreatType::API_HOOKING: return "API Hooking";
        case ThreatType::MEMORY_PATCHING: return "Memory Patching";
        case ThreatType::NETWORK_EXFILTRATION: return "Network Exfiltration";
        case ThreatType::FILE_MANIPULATION: return "File Manipulation";
        case ThreatType::REGISTRY_TAMPERING: return "Registry Tampering";
        case ThreatType::KEYLOGGER: return "Keylogger";
        case ThreatType::SCREEN_CAPTURE: return "Screen Capture";
        case ThreatType::PRIVILEGE_ESCALATION: return "Privilege Escalation";
        default: return "Unknown";
    }
}

std::string ProtectionEngine::ThreatSeverityToString(ThreatSeverity severity) {
    switch (severity) {
        case ThreatSeverity::LOW: return "LOW";
        case ThreatSeverity::MEDIUM: return "MEDIUM";
        case ThreatSeverity::HIGH: return "HIGH";
        case ThreatSeverity::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

std::string ProtectionEngine::ProtectionLevelToString(ProtectionLevel level) {
    switch (level) {
        case ProtectionLevel::DISABLED: return "DISABLED";
        case ProtectionLevel::MONITORING: return "MONITORING";
        case ProtectionLevel::ACTIVE: return "ACTIVE";
        case ProtectionLevel::AGGRESSIVE: return "AGGRESSIVE";
        default: return "UNKNOWN";
    }
}

void ProtectionEngine::InstallCodeInjectionHooks() {
    if (!m_hookingEngine) return;

    std::cout << "[ProtectionEngine] Installing code injection detection hooks..." << std::endl;

    // Hook CreateRemoteThread
    m_hookingEngine->InstallHook(
        "CreateRemoteThread",
        "kernel32.dll",
        "CreateRemoteThread",
        [this](const HookContext& context, void** parameters, size_t paramCount) -> bool {
            ThreatInfo threat;
            threat.type = ThreatType::CODE_INJECTION;
            threat.severity = ThreatSeverity::CRITICAL;
            threat.description = "CreateRemoteThread() called - Possible DLL injection attempt";
            threat.processId = context.processId;
            threat.threadId = context.threadId;
            threat.address = context.returnAddress;
            GetSystemTime(&threat.timestamp);

            OnThreatDetected(threat);

            if (m_config.level >= ProtectionLevel::AGGRESSIVE && m_config.blockThreats) {
                return true; // Block the call
            }

            return false;
        }
    );

    // Hook WriteProcessMemory
    m_hookingEngine->InstallHook(
        "WriteProcessMemory",
        "kernel32.dll",
        "WriteProcessMemory",
        [this](const HookContext& context, void** parameters, size_t paramCount) -> bool {
            ThreatInfo threat;
            threat.type = ThreatType::CODE_INJECTION;
            threat.severity = ThreatSeverity::HIGH;
            threat.description = "WriteProcessMemory() called - Monitoring for code injection";
            threat.processId = context.processId;
            threat.threadId = context.threadId;
            threat.address = context.returnAddress;
            GetSystemTime(&threat.timestamp);

            OnThreatDetected(threat);

            return false; // Allow but monitor
        }
    );

    // Hook VirtualAllocEx
    m_hookingEngine->InstallHook(
        "VirtualAllocEx",
        "kernel32.dll",
        "VirtualAllocEx",
        [this](const HookContext& context, void** parameters, size_t paramCount) -> bool {
            ThreatInfo threat;
            threat.type = ThreatType::CODE_INJECTION;
            threat.severity = ThreatSeverity::MEDIUM;
            threat.description = "VirtualAllocEx() called - Monitoring for memory allocation in remote process";
            threat.processId = context.processId;
            threat.threadId = context.threadId;
            threat.address = context.returnAddress;
            GetSystemTime(&threat.timestamp);

            OnThreatDetected(threat);

            return false;
        }
    );
}

void ProtectionEngine::InstallAPIHookingDetectionHooks() {
    if (!m_hookingEngine) return;

    std::cout << "[ProtectionEngine] Installing API hooking detection hooks..." << std::endl;

    // Hook VirtualProtect
    m_hookingEngine->InstallHook(
        "VirtualProtect",
        "kernel32.dll",
        "VirtualProtect",
        [this](const HookContext& context, void** parameters, size_t paramCount) -> bool {
            ThreatInfo threat;
            threat.type = ThreatType::API_HOOKING;
            threat.severity = ThreatSeverity::MEDIUM;
            threat.description = "VirtualProtect() called - Possible API hooking attempt";
            threat.processId = context.processId;
            threat.threadId = context.threadId;
            threat.address = context.returnAddress;
            GetSystemTime(&threat.timestamp);

            OnThreatDetected(threat);

            return false;
        }
    );

    // Hook LoadLibrary
    m_hookingEngine->InstallHook(
        "LoadLibraryA",
        "kernel32.dll",
        "LoadLibraryA",
        [this](const HookContext& context, void** parameters, size_t paramCount) -> bool {
            ThreatInfo threat;
            threat.type = ThreatType::API_HOOKING;
            threat.severity = ThreatSeverity::LOW;
            threat.description = "LoadLibraryA() called - Monitoring for suspicious library loading";
            threat.processId = context.processId;
            threat.threadId = context.threadId;
            threat.address = context.returnAddress;
            GetSystemTime(&threat.timestamp);

            OnThreatDetected(threat);

            return false;
        }
    );
}

void ProtectionEngine::InstallMemoryProtectionHooks() {
    if (!m_hookingEngine) return;

    std::cout << "[ProtectionEngine] Installing memory protection hooks..." << std::endl;

    // Hook VirtualAlloc
    m_hookingEngine->InstallHook(
        "VirtualAlloc",
        "kernel32.dll",
        "VirtualAlloc",
        [this](const HookContext& context, void** parameters, size_t paramCount) -> bool {
            ThreatInfo threat;
            threat.type = ThreatType::MEMORY_PATCHING;
            threat.severity = ThreatSeverity::LOW;
            threat.description = "VirtualAlloc() called - Monitoring memory allocation";
            threat.processId = context.processId;
            threat.threadId = context.threadId;
            threat.address = context.returnAddress;
            GetSystemTime(&threat.timestamp);

            OnThreatDetected(threat);

            return false;
        }
    );
}

void ProtectionEngine::InstallNetworkMonitoringHooks() {
    if (!m_hookingEngine) return;

    std::cout << "[ProtectionEngine] Installing network monitoring hooks..." << std::endl;

    // Hook WSASend
    m_hookingEngine->InstallHook(
        "WSASend",
        "ws2_32.dll",
        "WSASend",
        [this](const HookContext& context, void** parameters, size_t paramCount) -> bool {
            ThreatInfo threat;
            threat.type = ThreatType::NETWORK_EXFILTRATION;
            threat.severity = ThreatSeverity::MEDIUM;
            threat.description = "WSASend() called - Monitoring network data transmission";
            threat.processId = context.processId;
            threat.threadId = context.threadId;
            threat.address = context.returnAddress;
            GetSystemTime(&threat.timestamp);

            OnThreatDetected(threat);

            return false;
        }
    );

    // Hook connect
    m_hookingEngine->InstallHook(
        "connect",
        "ws2_32.dll",
        "connect",
        [this](const HookContext& context, void** parameters, size_t paramCount) -> bool {
            ThreatInfo threat;
            threat.type = ThreatType::NETWORK_EXFILTRATION;
            threat.severity = ThreatSeverity::LOW;
            threat.description = "connect() called - Monitoring network connections";
            threat.processId = context.processId;
            threat.threadId = context.threadId;
            threat.address = context.returnAddress;
            GetSystemTime(&threat.timestamp);

            OnThreatDetected(threat);

            return false;
        }
    );
}

void ProtectionEngine::UpdateProcessList() {
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) {
        return;
    }

    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    if (Process32First(snapshot, &pe32)) {
        std::lock_guard<std::mutex> lock(m_processesMutex);

        do {
            ProcessInfo info;
            info.processId = pe32.th32ProcessID;

            // Convert WCHAR to std::string
            char processName[MAX_PATH];
            WideCharToMultiByte(CP_UTF8, 0, pe32.szExeFile, -1, processName, MAX_PATH, nullptr, nullptr);
            info.processName = std::string(processName);

            info.isSuspicious = false;
            info.suspicionScore = 0.0;
            GetSystemTime(&info.creationTime);

            // Analyze process for suspicious behavior
            if (m_threatDetector) {
                info.suspicionScore = m_threatDetector->CalculateSuspicionScore(pe32.th32ProcessID);
                info.isSuspicious = info.suspicionScore > 0.5;
            }

            m_monitoredProcesses[pe32.th32ProcessID] = info;

            if (m_processCallback) {
                m_processCallback(info);
            }

        } while (Process32Next(snapshot, &pe32));
    }

    CloseHandle(snapshot);
}

void ProtectionEngine::PerformPeriodicAnalysis() {
    if (!m_threatDetector) return;

    // Scan current process for threats
    std::vector<MemoryScanResult> results = m_threatDetector->ScanProcess(GetCurrentProcessId());

    for (const auto& result : results) {
        ThreatInfo threat;
        threat.type = result.detectedThreat;
        threat.severity = result.severity;
        threat.description = result.description;
        threat.processId = GetCurrentProcessId();
        threat.address = result.address;
        threat.data = result.suspiciousData;
        threat.timestamp = result.scanTime;

        OnThreatDetected(threat);
    }
}

void ProtectionEngine::UpdateStatistics() {
    std::lock_guard<std::mutex> lock(m_statsMutex);

    // Calculate uptime
    SYSTEMTIME currentTime;
    GetSystemTime(&currentTime);
    FILETIME startFT, currentFT;
    SystemTimeToFileTime(&m_startTime, &startFT);
    SystemTimeToFileTime(&currentTime, &currentFT);

    ULARGE_INTEGER start, current;
    start.LowPart = startFT.dwLowDateTime;
    start.HighPart = startFT.dwHighDateTime;
    current.LowPart = currentFT.dwLowDateTime;
    current.HighPart = currentFT.dwHighDateTime;

    m_statistics.uptimeSeconds = static_cast<DWORD>((current.QuadPart - start.QuadPart) / 10000000);

    // Update threat statistics
    {
        std::lock_guard<std::mutex> threatLock(m_threatsMutex);
        m_statistics.totalThreats = m_detectedThreats.size();

        if (!m_detectedThreats.empty()) {
            m_statistics.lastThreatTime = m_detectedThreats.back().timestamp;

            double totalSeverity = 0.0;
            for (const auto& threat : m_detectedThreats) {
                totalSeverity += static_cast<double>(threat.severity);
            }
            m_statistics.averageThreatSeverity = totalSeverity / m_detectedThreats.size();
        }
    }

    // Update process statistics
    {
        std::lock_guard<std::mutex> processLock(m_processesMutex);
        m_statistics.monitoredProcesses = m_monitoredProcesses.size();
    }

    // Update hook statistics
    if (m_hookingEngine) {
        auto hookStats = m_hookingEngine->GetStatistics();
        m_statistics.installedHooks = hookStats.activeHooks;
    }
}
