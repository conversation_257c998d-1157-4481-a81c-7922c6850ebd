#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <mutex>
#include <atomic>
#include <unordered_map>

// Memory protection information
struct MemoryRegion {
    uintptr_t baseAddress;
    size_t size;
    DWORD originalProtection;
    DWORD currentProtection;
    std::string moduleName;
    bool isProtected;
    bool isSuspicious;
    SYSTEMTIME protectionTime;
};

// Memory access violation information
struct MemoryViolation {
    uintptr_t address;
    DWORD processId;
    DWORD threadId;
    std::string violationType;
    std::string description;
    SYSTEMTIME timestamp;
    std::vector<uintptr_t> callStack;
};

// Memory protection callback
using MemoryViolationCallback = std::function<void(const MemoryViolation&)>;

class MemoryProtector {
public:
    MemoryProtector();
    ~MemoryProtector();

    // Control methods
    bool Start();
    void Stop();
    bool IsRunning() const { return m_isRunning; }

    // Protection methods
    bool ProtectRegion(uintptr_t address, size_t size, DWORD protection);
    bool UnprotectRegion(uintptr_t address);
    bool IsRegionProtected(uintptr_t address) const;

    // Monitoring
    void SetCallback(MemoryViolationCallback callback);
    void EnableVirtualAllocMonitoring(bool enabled) { m_monitorVirtualAlloc = enabled; }
    void EnableVirtualProtectMonitoring(bool enabled) { m_monitorVirtualProtect = enabled; }
    void EnableWriteProcessMemoryMonitoring(bool enabled) { m_monitorWriteProcessMemory = enabled; }

    // Statistics
    size_t GetProtectedRegionsCount() const;
    size_t GetViolationsCount() const { return m_violationsCount; }
    std::vector<MemoryRegion> GetProtectedRegions() const;
    std::vector<MemoryViolation> GetRecentViolations() const;

    // Analysis
    bool AnalyzeMemoryLayout();
    bool DetectCodeInjection();
    bool DetectProcessHollowing();

private:
    // Exception handling
    static LONG WINAPI VectoredExceptionHandler(PEXCEPTION_POINTERS exceptionInfo);
    void HandleMemoryViolation(PEXCEPTION_POINTERS exceptionInfo);

    // Hook callbacks
    static LPVOID WINAPI HookedVirtualAlloc(LPVOID lpAddress, SIZE_T dwSize, DWORD flAllocationType, DWORD flProtect);
    static BOOL WINAPI HookedVirtualProtect(LPVOID lpAddress, SIZE_T dwSize, DWORD flNewProtect, PDWORD lpflOldProtect);
    static BOOL WINAPI HookedWriteProcessMemory(HANDLE hProcess, LPVOID lpBaseAddress, LPCVOID lpBuffer, SIZE_T nSize, SIZE_T* lpNumberOfBytesWritten);

    // Analysis methods
    bool IsSuspiciousAllocation(LPVOID address, SIZE_T size, DWORD protection);
    bool IsSuspiciousProtectionChange(LPVOID address, DWORD newProtection);
    bool IsCodeRegion(uintptr_t address) const;

    // Member variables
    std::atomic<bool> m_isRunning{false};
    std::atomic<bool> m_monitorVirtualAlloc{true};
    std::atomic<bool> m_monitorVirtualProtect{true};
    std::atomic<bool> m_monitorWriteProcessMemory{true};

    // Exception handler
    PVOID m_exceptionHandler = nullptr;

    // Protected regions
    mutable std::mutex m_regionsMutex;
    std::unordered_map<uintptr_t, MemoryRegion> m_protectedRegions;

    // Violations tracking
    mutable std::mutex m_violationsMutex;
    std::vector<MemoryViolation> m_recentViolations;
    std::atomic<size_t> m_violationsCount{0};
    static const size_t MAX_RECENT_VIOLATIONS = 500;

    // Callback
    MemoryViolationCallback m_callback;

    // Original function pointers
    static LPVOID (WINAPI* s_originalVirtualAlloc)(LPVOID, SIZE_T, DWORD, DWORD);
    static BOOL (WINAPI* s_originalVirtualProtect)(LPVOID, SIZE_T, DWORD, PDWORD);
    static BOOL (WINAPI* s_originalWriteProcessMemory)(HANDLE, LPVOID, LPCVOID, SIZE_T, SIZE_T*);

    // Static instance for exception handler
    static MemoryProtector* s_instance;
};
