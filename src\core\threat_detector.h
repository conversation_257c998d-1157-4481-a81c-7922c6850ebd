#pragma once

#include "protection_engine.h"
#include <windows.h>
#include <vector>
#include <string>
#include <memory>
#include <mutex>
#include <atomic>
#include <unordered_set>
#include <functional>

// Detection techniques
enum class DetectionTechnique {
    SIGNATURE_BASED,
    HEURISTIC_ANALYSIS,
    BEHAVIOR_ANALYSIS,
    ANOMALY_DETECTION,
    MACHINE_LEARNING,
    STATIC_ANALYSIS,
    DYNAMIC_ANALYSIS
};

// Detection confidence levels
enum class ConfidenceLevel {
    VERY_LOW = 1,
    LOW = 2,
    MEDIUM = 3,
    HIGH = 4,
    VERY_HIGH = 5
};

// Threat signature
struct ThreatSignature {
    std::string name;
    ThreatType type;
    ThreatSeverity severity;
    std::vector<uint8_t> pattern;
    std::vector<uint8_t> mask;
    size_t minSize;
    size_t maxSize;
    std::string description;
    DetectionTechnique technique;
    ConfidenceLevel confidence;
    bool isEnabled;
    
    ThreatSignature() : type(ThreatType::UNKNOWN), severity(ThreatSeverity::LOW),
                       minSize(0), maxSize(SIZE_MAX), technique(DetectionTechnique::SIGNATURE_BASED),
                       confidence(ConfidenceLevel::MEDIUM), isEnabled(true) {}
};

// Detection rule
struct DetectionRule {
    std::string name;
    std::string description;
    ThreatType threatType;
    ThreatSeverity severity;
    std::function<bool(const ProcessInfo&)> condition;
    DetectionTechnique technique;
    ConfidenceLevel confidence;
    bool isEnabled;
    size_t triggerCount;
    SYSTEMTIME lastTriggered;
    
    DetectionRule() : threatType(ThreatType::UNKNOWN), severity(ThreatSeverity::LOW),
                     technique(DetectionTechnique::HEURISTIC_ANALYSIS),
                     confidence(ConfidenceLevel::MEDIUM), isEnabled(true), triggerCount(0) {
        GetSystemTime(&lastTriggered);
    }
};

// Behavioral pattern
struct BehavioralPattern {
    std::string name;
    std::vector<std::string> apiSequence;
    std::vector<std::string> filePatterns;
    std::vector<std::string> registryPatterns;
    std::vector<std::string> networkPatterns;
    ThreatType associatedThreat;
    double suspicionWeight;
    size_t timeWindowMs;
    bool requiresSequentialOrder;
    
    BehavioralPattern() : associatedThreat(ThreatType::UNKNOWN), suspicionWeight(1.0),
                         timeWindowMs(30000), requiresSequentialOrder(false) {}
};

// Memory scan result
struct MemoryScanResult {
    uintptr_t address;
    size_t size;
    std::vector<ThreatSignature> matchedSignatures;
    std::vector<uint8_t> suspiciousData;
    ThreatType detectedThreat;
    ThreatSeverity severity;
    ConfidenceLevel confidence;
    std::string description;
    SYSTEMTIME scanTime;
};

/**
 * @brief Advanced Threat Detection System
 * 
 * Multi-layered threat detection engine using various techniques including
 * signature-based detection, heuristic analysis, behavioral analysis, and
 * machine learning for comprehensive malware detection.
 */
class ThreatDetector {
public:
    ThreatDetector();
    ~ThreatDetector();

    // Initialization
    bool Initialize();
    void Shutdown();
    bool IsInitialized() const { return m_initialized; }

    // Signature management
    bool LoadSignatures(const std::string& filePath);
    bool SaveSignatures(const std::string& filePath) const;
    bool AddSignature(const ThreatSignature& signature);
    bool RemoveSignature(const std::string& name);
    bool UpdateSignature(const std::string& name, const ThreatSignature& signature);
    std::vector<ThreatSignature> GetSignatures() const;
    std::vector<ThreatSignature> GetSignaturesByType(ThreatType type) const;

    // Detection rules management
    bool AddDetectionRule(const DetectionRule& rule);
    bool RemoveDetectionRule(const std::string& name);
    bool EnableDetectionRule(const std::string& name, bool enable = true);
    std::vector<DetectionRule> GetDetectionRules() const;
    std::vector<DetectionRule> GetActiveDetectionRules() const;

    // Behavioral patterns management
    bool AddBehavioralPattern(const BehavioralPattern& pattern);
    bool RemoveBehavioralPattern(const std::string& name);
    std::vector<BehavioralPattern> GetBehavioralPatterns() const;

    // Scanning functions
    std::vector<MemoryScanResult> ScanMemory(uintptr_t address, size_t size);
    std::vector<MemoryScanResult> ScanProcess(DWORD processId);
    std::vector<MemoryScanResult> ScanModule(HMODULE moduleHandle);
    std::vector<MemoryScanResult> ScanFile(const std::string& filePath);
    
    // Real-time detection
    bool StartRealTimeDetection();
    bool StopRealTimeDetection();
    bool IsRealTimeDetectionActive() const { return m_realTimeActive; }

    // Anti-debugging detection
    bool DetectDebugger();
    bool DetectRemoteDebugger(DWORD processId);
    bool DetectKernelDebugger();
    bool DetectHardwareBreakpoints();
    bool DetectSoftwareBreakpoints(uintptr_t address, size_t size);

    // Process hollowing detection
    bool DetectProcessHollowing(DWORD processId);
    bool DetectImageReplacement(DWORD processId);
    bool DetectSectionHollowing(DWORD processId);
    bool DetectManualDLLLoading(DWORD processId);

    // Code injection detection
    bool DetectCodeInjection(DWORD processId);
    bool DetectDLLInjection(DWORD processId);
    bool DetectProcessDoppelganging(DWORD processId);
    bool DetectAtomBombing(DWORD processId);
    bool DetectThreadHijacking(DWORD processId);

    // API hooking detection
    bool DetectAPIHooks(const std::string& moduleName);
    bool DetectInlineHooks(uintptr_t address, size_t size);
    bool DetectIATHooks(HMODULE moduleHandle);
    bool DetectEATHooks(HMODULE moduleHandle);

    // Memory protection detection
    bool DetectMemoryPatching(uintptr_t address, size_t size);
    bool DetectCodeCaves(HMODULE moduleHandle);
    bool DetectSuspiciousAllocations(DWORD processId);
    bool DetectHeapSpray(DWORD processId);

    // Network-based detection
    bool DetectSuspiciousConnections(DWORD processId);
    bool DetectDataExfiltration(DWORD processId);
    bool DetectC2Communication(DWORD processId);
    bool DetectDNSTunneling(DWORD processId);

    // File system detection
    bool DetectSuspiciousFileActivity(DWORD processId);
    bool DetectRansomwareActivity(DWORD processId);
    bool DetectFileEncryption(const std::string& directoryPath);
    bool DetectMassFileDeletion(DWORD processId);

    // Registry detection
    bool DetectRegistryTampering(DWORD processId);
    bool DetectPersistenceMechanisms(DWORD processId);
    bool DetectSecuritySoftwareDisabling(DWORD processId);
    bool DetectSystemConfigurationChanges(DWORD processId);

    // Behavioral analysis
    double CalculateSuspicionScore(DWORD processId);
    std::vector<std::string> GetSuspiciousBehaviors(DWORD processId);
    bool AnalyzeBehavioralPattern(DWORD processId, const BehavioralPattern& pattern);

    // Heuristic analysis
    bool PerformHeuristicAnalysis(const std::vector<uint8_t>& data);
    bool AnalyzeEntropy(const std::vector<uint8_t>& data);
    bool AnalyzeStringPatterns(const std::vector<uint8_t>& data);
    bool AnalyzeImportTable(HMODULE moduleHandle);
    bool AnalyzeExportTable(HMODULE moduleHandle);

    // Machine learning integration
    bool LoadMLModel(const std::string& modelPath);
    double PredictThreatProbability(const std::vector<double>& features);
    std::vector<double> ExtractFeatures(DWORD processId);

    // Statistics and reporting
    struct DetectionStatistics {
        size_t totalScans = 0;
        size_t threatsDetected = 0;
        size_t falsePositives = 0;
        size_t signaturesLoaded = 0;
        size_t rulesActive = 0;
        size_t patternsActive = 0;
        double averageConfidence = 0.0;
        SYSTEMTIME lastDetection = {};
        std::unordered_map<ThreatType, size_t> threatsByType;
        std::unordered_map<DetectionTechnique, size_t> detectionsByTechnique;
    };
    DetectionStatistics GetStatistics() const;

    // Configuration
    void SetScanTimeout(DWORD timeoutMs) { m_scanTimeout = timeoutMs; }
    void SetMaxScanSize(size_t maxSize) { m_maxScanSize = maxSize; }
    void SetMinConfidenceLevel(ConfidenceLevel level) { m_minConfidence = level; }
    void EnableTechnique(DetectionTechnique technique, bool enable = true);

    // Event callbacks
    using ThreatDetectedCallback = std::function<void(const ThreatInfo&)>;
    void SetThreatDetectedCallback(ThreatDetectedCallback callback) { m_threatCallback = callback; }

private:
    bool m_initialized;
    std::atomic<bool> m_realTimeActive{false};
    
    // Signature database
    mutable std::mutex m_signaturesMutex;
    std::vector<ThreatSignature> m_signatures;
    std::unordered_set<std::string> m_signatureNames;
    
    // Detection rules
    mutable std::mutex m_rulesMutex;
    std::vector<DetectionRule> m_detectionRules;
    
    // Behavioral patterns
    mutable std::mutex m_patternsMutex;
    std::vector<BehavioralPattern> m_behavioralPatterns;
    
    // Configuration
    DWORD m_scanTimeout;
    size_t m_maxScanSize;
    ConfidenceLevel m_minConfidence;
    std::unordered_set<DetectionTechnique> m_enabledTechniques;
    
    // Statistics
    mutable std::mutex m_statsMutex;
    DetectionStatistics m_statistics;
    
    // Callbacks
    ThreatDetectedCallback m_threatCallback;
    
    // Real-time monitoring
    std::vector<HANDLE> m_monitoringThreads;
    std::atomic<bool> m_shouldStop{false};
    
    // Internal methods
    bool ScanMemoryRegion(uintptr_t address, size_t size, std::vector<MemoryScanResult>& results);
    bool MatchSignature(const std::vector<uint8_t>& data, const ThreatSignature& signature);
    bool EvaluateDetectionRule(const DetectionRule& rule, const ProcessInfo& processInfo);

    void UpdateStatistics();
    void OnThreatDetected(const ThreatInfo& threat);

    // Initialization helpers
    void LoadDefaultSignatures();
    void LoadDefaultDetectionRules();
    void LoadDefaultBehavioralPatterns();

    // Scanning helpers
    void ScanWithSignatures(const std::vector<uint8_t>& data, uintptr_t baseAddress,
                           std::vector<MemoryScanResult>& results);
    void PerformHeuristicAnalysis(const std::vector<uint8_t>& data, uintptr_t baseAddress,
                                 std::vector<MemoryScanResult>& results);

    // Thread functions
    static DWORD WINAPI RealTimeMonitoringThread(LPVOID param);
    static DWORD WINAPI BehaviorAnalysisThread(LPVOID param);

    // Utility functions
    std::vector<uint8_t> ReadProcessMemory(DWORD processId, uintptr_t address, size_t size);
    std::vector<HMODULE> GetProcessModules(DWORD processId);
    std::string GetProcessImagePath(DWORD processId);
    std::vector<std::string> GetProcessCommandLine(DWORD processId);

    // Statistics
    std::atomic<uint64_t> m_totalScans{0};
};
