#pragma once

#include <windows.h>
#include <vector>
#include <string>
#include <memory>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <functional>

// Forward declarations
class HookingEngine;
class ThreatDetector;
#include "behavior_analyzer.h"
#include "memory_protector.h"
#include "network_monitor.h"

// Protection levels
enum class ProtectionLevel {
    DISABLED = 0,
    MONITORING = 1,
    ACTIVE = 2,
    AGGRESSIVE = 3
};

// Threat types
enum class ThreatType {
    UNKNOWN = 0,
    DEBUGGER_DETECTION,
    PROCESS_HOLLOWING,
    CODE_INJECTION,
    API_HOOKING,
    MEMORY_PATCHING,
    NETWORK_EXFILTRATION,
    FILE_MANIPULATION,
    REGISTRY_TAMPERING,
    KEYLOGGER,
    SCREEN_CAPTURE,
    PRIVILEGE_ESCALATION
};

// Threat severity
enum class ThreatSeverity {
    LOW = 1,
    MEDIUM = 2,
    HIGH = 3,
    CRITICAL = 4
};

// Protection configuration
struct ProtectionConfig {
    ProtectionLevel level = ProtectionLevel::MONITORING;
    bool enableAntiDebugging = true;
    bool enableProcessHollowingDetection = true;
    bool enableCodeInjectionDetection = true;
    bool enableAPIHookingDetection = true;
    bool enableMemoryProtection = true;
    bool enableNetworkMonitoring = true;
    bool enableFileSystemProtection = true;
    bool enableRegistryProtection = true;
    bool enableKeystrokeProtection = true;
    bool enableScreenCaptureProtection = true;
    bool enablePrivilegeEscalationDetection = true;
    bool enableBehaviorAnalysis = true;
    bool enableRealTimeScanning = true;
    bool enableAdvancedHeuristics = true;
    
    // Response actions
    bool blockThreats = true;
    bool logThreats = true;
    bool alertUser = true;
    bool quarantineThreats = false;
    bool terminateThreats = false;
};

// Threat information
struct ThreatInfo {
    ThreatType type;
    ThreatSeverity severity;
    std::string description;
    std::string source;
    DWORD processId;
    DWORD threadId;
    uintptr_t address;
    std::vector<uint8_t> data;
    SYSTEMTIME timestamp;
    std::string additionalInfo;
};

// Hook information
struct HookInfo {
    std::string apiName;
    std::string moduleName;
    uintptr_t originalAddress;
    uintptr_t hookAddress;
    std::vector<uint8_t> originalBytes;
    std::vector<uint8_t> hookBytes;
    bool isActive;
    SYSTEMTIME installTime;
};

// Process information
struct ProcessInfo {
    DWORD processId;
    std::string processName;
    std::string commandLine;
    std::string imagePath;
    uintptr_t baseAddress;
    SIZE_T imageSize;
    SYSTEMTIME creationTime;
    std::vector<std::string> loadedModules;
    bool isSuspicious;
    double suspicionScore;
};

// Event callback types
using ThreatCallback = std::function<void(const ThreatInfo&)>;
using ProcessCallback = std::function<void(const ProcessInfo&)>;
using HookEventCallback = std::function<void(const HookInfo&)>;

/**
 * @brief Advanced Protection Engine - Core security system
 * 
 * This is the main protection engine that coordinates all security components
 * and provides a unified interface for threat detection and response.
 */
class ProtectionEngine {
public:
    ProtectionEngine();
    ~ProtectionEngine();

    // Core functionality
    bool Initialize();
    void Shutdown();
    bool IsInitialized() const { return m_initialized; }

    // Protection control
    bool StartProtection();
    bool StopProtection();
    bool IsProtectionActive() const { return m_protectionActive; }

    // Configuration
    void SetConfiguration(const ProtectionConfig& config);
    const ProtectionConfig& GetConfiguration() const { return m_config; }

    // Threat management
    std::vector<ThreatInfo> GetDetectedThreats() const;
    std::vector<ThreatInfo> GetRecentThreats(DWORD minutes = 60) const;
    void ClearThreatHistory();
    size_t GetThreatCount() const;

    // Process monitoring
    std::vector<ProcessInfo> GetMonitoredProcesses() const;
    std::vector<ProcessInfo> GetSuspiciousProcesses() const;
    ProcessInfo GetProcessInfo(DWORD processId) const;

    // Hook management
    std::vector<HookInfo> GetInstalledHooks() const;
    bool InstallHook(const std::string& apiName, const std::string& moduleName, void* hookFunction);
    bool RemoveHook(const std::string& apiName, const std::string& moduleName);
    void RemoveAllHooks();

    // Event callbacks
    void SetThreatCallback(ThreatCallback callback) { m_threatCallback = callback; }
    void SetProcessCallback(ProcessCallback callback) { m_processCallback = callback; }
    void SetHookCallback(HookEventCallback callback) { m_hookCallback = callback; }

    // Statistics
    struct Statistics {
        size_t totalThreats = 0;
        size_t blockedThreats = 0;
        size_t monitoredProcesses = 0;
        size_t installedHooks = 0;
        SYSTEMTIME lastThreatTime = {};
        double averageThreatSeverity = 0.0;
        DWORD uptimeSeconds = 0;
    };
    Statistics GetStatistics() const;

    // Advanced features
    bool ScanProcess(DWORD processId);
    bool ScanMemoryRegion(uintptr_t address, SIZE_T size);
    bool AnalyzeBehavior(DWORD processId, DWORD timeoutMs = 30000);
    
    // Utility functions
    static std::string ThreatTypeToString(ThreatType type);
    static std::string ThreatSeverityToString(ThreatSeverity severity);
    static std::string ProtectionLevelToString(ProtectionLevel level);

private:
    // Internal state
    std::atomic<bool> m_initialized{false};
    std::atomic<bool> m_protectionActive{false};
    ProtectionConfig m_config;
    
    // Core components
    std::unique_ptr<HookingEngine> m_hookingEngine;
    std::unique_ptr<ThreatDetector> m_threatDetector;
    std::unique_ptr<BehaviorAnalyzer> m_behaviorAnalyzer;
    std::unique_ptr<MemoryProtector> m_memoryProtector;
    std::unique_ptr<NetworkMonitor> m_networkMonitor;
    
    // Data storage
    mutable std::mutex m_threatsMutex;
    std::vector<ThreatInfo> m_detectedThreats;
    
    mutable std::mutex m_processesMutex;
    std::unordered_map<DWORD, ProcessInfo> m_monitoredProcesses;
    
    mutable std::mutex m_hooksMutex;
    std::vector<HookInfo> m_installedHooks;
    
    // Event callbacks
    ThreatCallback m_threatCallback;
    ProcessCallback m_processCallback;
    HookEventCallback m_hookCallback;
    
    // Statistics
    mutable std::mutex m_statsMutex;
    Statistics m_statistics;
    SYSTEMTIME m_startTime;
    
    // Internal methods
    void OnThreatDetected(const ThreatInfo& threat);
    void OnProcessDetected(const ProcessInfo& process);
    void OnHookInstalled(const HookInfo& hook);

    void UpdateStatistics();
    bool InitializeComponents();
    void ShutdownComponents();

    // Hook installation methods
    void InstallAntiDebuggingHooks();
    void InstallProcessHollowingHooks();
    void InstallCodeInjectionHooks();
    void InstallAPIHookingDetectionHooks();
    void InstallMemoryProtectionHooks();
    void InstallNetworkMonitoringHooks();

    // Monitoring methods
    void StartMonitoringThreads();
    void UpdateProcessList();
    void PerformPeriodicAnalysis();

    // Thread management
    std::vector<HANDLE> m_workerThreads;
    std::atomic<bool> m_shouldStop{false};

    static DWORD WINAPI MonitoringThread(LPVOID param);
    static DWORD WINAPI AnalysisThread(LPVOID param);
};
