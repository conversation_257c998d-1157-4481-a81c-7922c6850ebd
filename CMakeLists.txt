# This file is automatically generated from cmake.toml - DO NOT EDIT
# See https://github.com/build-cpp/cmkr for more information

cmake_minimum_required(VERSION 3.15)

if(CMAKE_SOURCE_DIR STREQUAL CMAKE_BINARY_DIR)
	message(FATAL_ERROR "In-tree builds are not supported. Run CMake from a separate directory: cmake -B build")
endif()

set(CMKR_ROOT_PROJECT OFF)
if(CMAKE_CURRENT_SOURCE_DIR STREQUAL CMAKE_SOURCE_DIR)
	set(CMKR_ROOT_PROJECT ON)

	# Bootstrap cmkr and automatically regenerate CMakeLists.txt
	include("cmake/cmkr.cmake" OPTIONAL RESULT_VARIABLE CMKR_INCLUDE_RESULT)
	if(CMKR_INCLUDE_RESULT)
		cmkr()
	endif()

	# Enable folder support
	set_property(GLOBAL PROPERTY USE_FOLDERS ON)

	# Create a configure-time dependency on cmake.toml to improve IDE support
	configure_file(cmake.toml cmake.toml COPYONLY)
endif()

include("cmake/msvc-static-runtime.cmake")
include("cmake/msvc-configurations.cmake")

project(AdvancedProtectionPlugin
	LANGUAGES
		CXX
	DESCRIPTION
		"Advanced Protection Plugin v2.0 for x64dbg - Professional Security System"
)

include(FetchContent)

# Fix warnings about DOWNLOAD_EXTRACT_TIMESTAMP
if(POLICY CMP0135)
	cmake_policy(SET CMP0135 NEW)
endif()
message(STATUS "Fetching x64dbg...")
FetchContent_Declare(x64dbg
	URL
		"https://sourceforge.net/projects/x64dbg/files/snapshots/snapshot_2023-06-10_18-05.zip"
	URL_HASH
		SHA1=04468bd61fb36d6b10d17f342f03ef12f5b2ce62
)
FetchContent_MakeAvailable(x64dbg)

include("cmake/x64dbg.cmake")

# Target: AdvancedProtectionPlugin
set(AdvancedProtectionPlugin_SOURCES
	"src/pluginmain.cpp"
	"src/plugin.cpp"
	cmake.toml
)

x64dbg_plugin(AdvancedProtectionPlugin)

target_sources(AdvancedProtectionPlugin PRIVATE ${AdvancedProtectionPlugin_SOURCES})
source_group(TREE ${CMAKE_CURRENT_SOURCE_DIR} FILES ${AdvancedProtectionPlugin_SOURCES})

target_compile_definitions(AdvancedProtectionPlugin PUBLIC
	UNICODE
	_UNICODE
	WIN32_LEAN_AND_MEAN
	_CRT_SECURE_NO_WARNINGS
)

target_include_directories(AdvancedProtectionPlugin PUBLIC
	src
)

target_link_libraries(AdvancedProtectionPlugin PUBLIC
	user32
	kernel32
)

