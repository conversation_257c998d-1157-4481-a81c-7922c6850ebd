#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <mutex>
#include <thread>
#include <atomic>

// Network activity information
struct NetworkActivity {
    std::string processName;
    DWORD processId;
    std::string localAddress;
    std::string remoteAddress;
    DWORD localPort;
    DWORD remotePort;
    std::string protocol;
    SYSTEMTIME timestamp;
    bool isSuspicious;
    std::string suspicionReason;
};

// Network monitoring callback
using NetworkCallback = std::function<void(const NetworkActivity&)>;

class NetworkMonitor {
public:
    NetworkMonitor();
    ~NetworkMonitor();

    // Control methods
    bool Start();
    void Stop();
    bool IsRunning() const { return m_isRunning; }

    // Configuration
    void SetCallback(NetworkCallback callback);
    void AddSuspiciousPort(DWORD port);
    void AddSuspiciousAddress(const std::string& address);
    void SetMonitoringEnabled(bool enabled) { m_monitoringEnabled = enabled; }

    // Statistics
    size_t GetTotalConnections() const { return m_totalConnections; }
    size_t GetSuspiciousConnections() const { return m_suspiciousConnections; }
    std::vector<NetworkActivity> GetRecentActivity() const;

private:
    // Monitoring thread
    void MonitoringThread();
    
    // Analysis methods
    bool AnalyzeConnection(const NetworkActivity& activity);
    bool IsSuspiciousPort(DWORD port) const;
    bool IsSuspiciousAddress(const std::string& address) const;

    // Member variables
    std::atomic<bool> m_isRunning{false};
    std::atomic<bool> m_monitoringEnabled{true};
    std::thread m_monitoringThread;
    NetworkCallback m_callback;
    
    // Suspicious indicators
    std::vector<DWORD> m_suspiciousPorts;
    std::vector<std::string> m_suspiciousAddresses;
    
    // Statistics
    std::atomic<size_t> m_totalConnections{0};
    std::atomic<size_t> m_suspiciousConnections{0};
    
    // Recent activity storage
    mutable std::mutex m_activityMutex;
    std::vector<NetworkActivity> m_recentActivity;
    static const size_t MAX_RECENT_ACTIVITIES = 1000;
};
