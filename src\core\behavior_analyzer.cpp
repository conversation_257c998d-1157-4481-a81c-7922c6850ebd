#include "behavior_analyzer.h"
#include <iostream>
#include <algorithm>
#include <psapi.h>
#include <tlhelp32.h>

BehaviorAnalyzer::BehaviorAnalyzer() {
    InitializePatterns();
}

BehaviorAnalyzer::~BehaviorAnalyzer() {
    Stop();
}

bool BehaviorAnalyzer::Start() {
    if (m_isRunning) {
        return true;
    }

    try {
        m_isRunning = true;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[BehaviorAnalyzer] Failed to start: " << e.what() << std::endl;
        return false;
    }
}

void BehaviorAnalyzer::Stop() {
    if (!m_isRunning) {
        return;
    }

    m_isRunning = false;
}

void BehaviorAnalyzer::AnalyzeProcess(DWORD processId) {
    if (!m_isRunning) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_processesMutex);
    
    if (m_processMap.find(processId) == m_processMap.end()) {
        ProcessBehavior behavior;
        behavior.processId = processId;
        behavior.processName = GetProcessName(processId);
        behavior.imagePath = GetProcessPath(processId);
        behavior.suspicionScore = 0.0f;
        behavior.isSuspicious = false;
        GetSystemTime(&behavior.startTime);
        
        m_processMap[processId] = behavior;
        m_analyzedProcesses++;
    }
}

void BehaviorAnalyzer::AnalyzeApiCall(DWORD processId, const std::string& apiName, const std::vector<std::string>& parameters) {
    if (!m_isRunning || !m_realTimeAnalysis) {
        return;
    }

    BehaviorEvent event;
    event.processId = processId;
    event.eventType = "API_CALL";
    event.description = "API call detected: " + apiName;
    event.details = apiName;
    event.severity = 0.1f;
    GetSystemTime(&event.timestamp);

    // Check for suspicious API calls
    if (apiName == "CreateRemoteThread" || apiName == "WriteProcessMemory" || 
        apiName == "VirtualAllocEx" || apiName == "SetWindowsHookEx") {
        event.severity = 0.8f;
        event.description = "Suspicious API call detected: " + apiName;
    }

    UpdateProcessBehavior(processId, event);
}

void BehaviorAnalyzer::AnalyzeFileAccess(DWORD processId, const std::string& filePath, const std::string& operation) {
    if (!m_isRunning || !m_realTimeAnalysis) {
        return;
    }

    BehaviorEvent event;
    event.processId = processId;
    event.eventType = "FILE_ACCESS";
    event.description = "File access: " + operation + " on " + filePath;
    event.details = filePath;
    event.severity = 0.1f;
    GetSystemTime(&event.timestamp);

    // Check for suspicious file operations
    if (filePath.find(".exe") != std::string::npos && operation == "WRITE") {
        event.severity = 0.7f;
        event.description = "Suspicious file write to executable: " + filePath;
    }

    UpdateProcessBehavior(processId, event);
}

void BehaviorAnalyzer::AnalyzeRegistryAccess(DWORD processId, const std::string& keyPath, const std::string& operation) {
    if (!m_isRunning || !m_realTimeAnalysis) {
        return;
    }

    BehaviorEvent event;
    event.processId = processId;
    event.eventType = "REGISTRY_ACCESS";
    event.description = "Registry access: " + operation + " on " + keyPath;
    event.details = keyPath;
    event.severity = 0.1f;
    GetSystemTime(&event.timestamp);

    // Check for suspicious registry operations
    if (keyPath.find("\\Run") != std::string::npos || keyPath.find("\\RunOnce") != std::string::npos) {
        event.severity = 0.6f;
        event.description = "Suspicious registry access to startup location: " + keyPath;
    }

    UpdateProcessBehavior(processId, event);
}

void BehaviorAnalyzer::AnalyzeNetworkActivity(DWORD processId, const std::string& address, DWORD port, const std::string& protocol) {
    if (!m_isRunning || !m_realTimeAnalysis) {
        return;
    }

    BehaviorEvent event;
    event.processId = processId;
    event.eventType = "NETWORK_ACTIVITY";
    event.description = "Network connection: " + protocol + " to " + address + ":" + std::to_string(port);
    event.details = address + ":" + std::to_string(port);
    event.severity = 0.2f;
    GetSystemTime(&event.timestamp);

    // Check for suspicious network activity
    if (port == 1337 || port == 31337 || port == 4444 || port == 6666) {
        event.severity = 0.9f;
        event.description = "Suspicious network connection to known malicious port: " + std::to_string(port);
    }

    UpdateProcessBehavior(processId, event);
}

bool BehaviorAnalyzer::DetectCodeInjection(DWORD processId) {
    std::lock_guard<std::mutex> lock(m_processesMutex);
    auto it = m_processMap.find(processId);
    if (it == m_processMap.end()) {
        return false;
    }

    const auto& behavior = it->second;
    
    // Look for code injection indicators
    bool hasCreateRemoteThread = std::find(behavior.apiCalls.begin(), behavior.apiCalls.end(), "CreateRemoteThread") != behavior.apiCalls.end();
    bool hasWriteProcessMemory = std::find(behavior.apiCalls.begin(), behavior.apiCalls.end(), "WriteProcessMemory") != behavior.apiCalls.end();
    bool hasVirtualAllocEx = std::find(behavior.apiCalls.begin(), behavior.apiCalls.end(), "VirtualAllocEx") != behavior.apiCalls.end();

    return hasCreateRemoteThread && hasWriteProcessMemory && hasVirtualAllocEx;
}

bool BehaviorAnalyzer::DetectProcessHollowing(DWORD processId) {
    std::lock_guard<std::mutex> lock(m_processesMutex);
    auto it = m_processMap.find(processId);
    if (it == m_processMap.end()) {
        return false;
    }

    const auto& behavior = it->second;
    
    // Look for process hollowing indicators
    bool hasNtUnmapViewOfSection = std::find(behavior.apiCalls.begin(), behavior.apiCalls.end(), "NtUnmapViewOfSection") != behavior.apiCalls.end();
    bool hasCreateProcess = std::find(behavior.apiCalls.begin(), behavior.apiCalls.end(), "CreateProcess") != behavior.apiCalls.end();
    bool hasWriteProcessMemory = std::find(behavior.apiCalls.begin(), behavior.apiCalls.end(), "WriteProcessMemory") != behavior.apiCalls.end();

    return hasNtUnmapViewOfSection && hasCreateProcess && hasWriteProcessMemory;
}

bool BehaviorAnalyzer::DetectKeylogging(DWORD processId) {
    std::lock_guard<std::mutex> lock(m_processesMutex);
    auto it = m_processMap.find(processId);
    if (it == m_processMap.end()) {
        return false;
    }

    const auto& behavior = it->second;
    
    // Look for keylogging indicators
    bool hasSetWindowsHook = std::find(behavior.apiCalls.begin(), behavior.apiCalls.end(), "SetWindowsHookEx") != behavior.apiCalls.end();
    bool hasGetAsyncKeyState = std::find(behavior.apiCalls.begin(), behavior.apiCalls.end(), "GetAsyncKeyState") != behavior.apiCalls.end();

    return hasSetWindowsHook || hasGetAsyncKeyState;
}

bool BehaviorAnalyzer::DetectScreenCapture(DWORD processId) {
    std::lock_guard<std::mutex> lock(m_processesMutex);
    auto it = m_processMap.find(processId);
    if (it == m_processMap.end()) {
        return false;
    }

    const auto& behavior = it->second;
    
    // Look for screen capture indicators
    bool hasBitBlt = std::find(behavior.apiCalls.begin(), behavior.apiCalls.end(), "BitBlt") != behavior.apiCalls.end();
    bool hasGetDC = std::find(behavior.apiCalls.begin(), behavior.apiCalls.end(), "GetDC") != behavior.apiCalls.end();

    return hasBitBlt && hasGetDC;
}

bool BehaviorAnalyzer::DetectCryptocurrency(DWORD processId) {
    std::lock_guard<std::mutex> lock(m_processesMutex);
    auto it = m_processMap.find(processId);
    if (it == m_processMap.end()) {
        return false;
    }

    const auto& behavior = it->second;
    
    // Look for cryptocurrency mining indicators
    for (const auto& connection : behavior.networkConnections) {
        if (connection.find("pool") != std::string::npos || 
            connection.find("mining") != std::string::npos ||
            connection.find(":4444") != std::string::npos ||
            connection.find(":8080") != std::string::npos) {
            return true;
        }
    }

    return false;
}

bool BehaviorAnalyzer::DetectRansomware(DWORD processId) {
    std::lock_guard<std::mutex> lock(m_processesMutex);
    auto it = m_processMap.find(processId);
    if (it == m_processMap.end()) {
        return false;
    }

    const auto& behavior = it->second;
    
    // Look for ransomware indicators
    int encryptionFileCount = 0;
    for (const auto& file : behavior.fileAccesses) {
        if (file.find(".encrypted") != std::string::npos ||
            file.find(".locked") != std::string::npos ||
            file.find("README") != std::string::npos) {
            encryptionFileCount++;
        }
    }

    return encryptionFileCount > 5; // Multiple encryption-related file operations
}

void BehaviorAnalyzer::SetCallback(BehaviorCallback callback) {
    m_callback = callback;
}

size_t BehaviorAnalyzer::GetAnalyzedProcessesCount() const {
    return m_analyzedProcesses;
}

size_t BehaviorAnalyzer::GetSuspiciousProcessesCount() const {
    return m_suspiciousProcesses;
}

size_t BehaviorAnalyzer::GetDetectedPatternsCount() const {
    return m_detectedPatternsCount;
}

std::vector<ProcessBehavior> BehaviorAnalyzer::GetSuspiciousProcesses() const {
    std::lock_guard<std::mutex> lock(m_processesMutex);
    std::vector<ProcessBehavior> suspicious;
    
    for (const auto& pair : m_processMap) {
        if (pair.second.isSuspicious) {
            suspicious.push_back(pair.second);
        }
    }
    
    return suspicious;
}

std::vector<BehaviorPattern> BehaviorAnalyzer::GetDetectedPatterns() const {
    std::lock_guard<std::mutex> lock(m_patternsMutex);
    return m_detectedPatterns;
}

std::vector<BehaviorEvent> BehaviorAnalyzer::GetRecentEvents() const {
    std::lock_guard<std::mutex> lock(m_eventsMutex);
    std::vector<BehaviorEvent> events;
    
    std::queue<BehaviorEvent> tempQueue = m_recentEvents;
    while (!tempQueue.empty()) {
        events.push_back(tempQueue.front());
        tempQueue.pop();
    }
    
    return events;
}

void BehaviorAnalyzer::UpdateProcessBehavior(DWORD processId, const BehaviorEvent& event) {
    {
        std::lock_guard<std::mutex> lock(m_processesMutex);
        auto it = m_processMap.find(processId);
        if (it != m_processMap.end()) {
            auto& behavior = it->second;
            
            if (event.eventType == "API_CALL") {
                behavior.apiCalls.push_back(event.details);
            } else if (event.eventType == "FILE_ACCESS") {
                behavior.fileAccesses.push_back(event.details);
            } else if (event.eventType == "REGISTRY_ACCESS") {
                behavior.registryAccesses.push_back(event.details);
            } else if (event.eventType == "NETWORK_ACTIVITY") {
                behavior.networkConnections.push_back(event.details);
            }
            
            behavior.suspicionScore = CalculateSuspicionScore(behavior);
            behavior.isSuspicious = behavior.suspicionScore >= m_suspicionThreshold;
            
            if (behavior.isSuspicious) {
                m_suspiciousProcesses++;
            }
        }
    }

    // Store recent event
    {
        std::lock_guard<std::mutex> lock(m_eventsMutex);
        m_recentEvents.push(event);
        if (m_recentEvents.size() > MAX_RECENT_EVENTS) {
            m_recentEvents.pop();
        }
    }

    // Notify callback
    if (m_callback) {
        m_callback(event);
    }
}

float BehaviorAnalyzer::CalculateSuspicionScore(const ProcessBehavior& behavior) {
    float score = 0.0f;
    
    // Weight different types of activities
    score += behavior.apiCalls.size() * 0.01f;
    score += behavior.fileAccesses.size() * 0.005f;
    score += behavior.registryAccesses.size() * 0.01f;
    score += behavior.networkConnections.size() * 0.02f;
    
    // Cap the score at 1.0
    return std::min(score, 1.0f);
}

void BehaviorAnalyzer::InitializePatterns() {
    std::lock_guard<std::mutex> lock(m_patternsMutex);
    
    m_knownPatterns.push_back(CreateCodeInjectionPattern());
    m_knownPatterns.push_back(CreateProcessHollowingPattern());
    m_knownPatterns.push_back(CreateKeyloggingPattern());
    m_knownPatterns.push_back(CreateScreenCapturePattern());
    m_knownPatterns.push_back(CreateCryptocurrencyPattern());
    m_knownPatterns.push_back(CreateRansomwarePattern());
}

BehaviorPattern BehaviorAnalyzer::CreateCodeInjectionPattern() {
    BehaviorPattern pattern;
    pattern.name = "Code Injection";
    pattern.description = "Process injection techniques detected";
    pattern.severity = 0.9f;
    pattern.indicators = {"CreateRemoteThread", "WriteProcessMemory", "VirtualAllocEx"};
    pattern.isActive = true;
    return pattern;
}

BehaviorPattern BehaviorAnalyzer::CreateProcessHollowingPattern() {
    BehaviorPattern pattern;
    pattern.name = "Process Hollowing";
    pattern.description = "Process hollowing technique detected";
    pattern.severity = 0.95f;
    pattern.indicators = {"NtUnmapViewOfSection", "CreateProcess", "WriteProcessMemory"};
    pattern.isActive = true;
    return pattern;
}

BehaviorPattern BehaviorAnalyzer::CreateKeyloggingPattern() {
    BehaviorPattern pattern;
    pattern.name = "Keylogging";
    pattern.description = "Keylogging activity detected";
    pattern.severity = 0.8f;
    pattern.indicators = {"SetWindowsHookEx", "GetAsyncKeyState"};
    pattern.isActive = true;
    return pattern;
}

BehaviorPattern BehaviorAnalyzer::CreateScreenCapturePattern() {
    BehaviorPattern pattern;
    pattern.name = "Screen Capture";
    pattern.description = "Screen capture activity detected";
    pattern.severity = 0.6f;
    pattern.indicators = {"BitBlt", "GetDC"};
    pattern.isActive = true;
    return pattern;
}

BehaviorPattern BehaviorAnalyzer::CreateCryptocurrencyPattern() {
    BehaviorPattern pattern;
    pattern.name = "Cryptocurrency Mining";
    pattern.description = "Cryptocurrency mining activity detected";
    pattern.severity = 0.7f;
    pattern.indicators = {"pool", "mining", ":4444", ":8080"};
    pattern.isActive = true;
    return pattern;
}

BehaviorPattern BehaviorAnalyzer::CreateRansomwarePattern() {
    BehaviorPattern pattern;
    pattern.name = "Ransomware";
    pattern.description = "Ransomware activity detected";
    pattern.severity = 1.0f;
    pattern.indicators = {".encrypted", ".locked", "README"};
    pattern.isActive = true;
    return pattern;
}

std::string BehaviorAnalyzer::GetProcessName(DWORD processId) {
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
    if (!hProcess) {
        return "Unknown";
    }

    char processName[MAX_PATH];
    if (GetModuleBaseNameA(hProcess, NULL, processName, sizeof(processName))) {
        CloseHandle(hProcess);
        return std::string(processName);
    }

    CloseHandle(hProcess);
    return "Unknown";
}

std::string BehaviorAnalyzer::GetProcessPath(DWORD processId) {
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
    if (!hProcess) {
        return "Unknown";
    }

    char processPath[MAX_PATH];
    if (GetModuleFileNameExA(hProcess, NULL, processPath, sizeof(processPath))) {
        CloseHandle(hProcess);
        return std::string(processPath);
    }

    CloseHandle(hProcess);
    return "Unknown";
}
