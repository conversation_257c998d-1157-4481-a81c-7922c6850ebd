# Advanced Protection Plugin v2.0 - دليل التثبيت

## متطلبات النظام
- Windows 10/11 (64-bit)
- x64dbg (أحدث إصدار)
- Visual Studio 2022 Redistributable

## خطوات التثبيت

### 1. نسخ ملف البرنامج المساعد
```
انسخ الملف: build/Release/AdvancedProtectionPlugin.dp64
إلى مجلد: [x64dbg installation]/plugins/
```

### 2. إعادة تشغيل x64dbg
- أغلق x64dbg إذا كان مفتوحاً
- افتح x64dbg مرة أخرى

### 3. التحقق من التثبيت
- في x64dbg، اذهب إلى قائمة "Plugins"
- يجب أن ترى "Advanced Protection v2.0"

## استخدام البرنامج المساعد

### القوائم المتاحة:
1. **Toggle Protection** - تفعيل/إيقاف الحماية
2. **Show Protection UI** - عرض واجهة الحماية
3. **Configuration** - إعدادات البرنامج
4. **About** - معلومات حول البرنامج

### الحالة الحالية:
- ✅ تم البناء بنجاح (494 KB)
- ✅ هيكل مبسط للتوافق الأقصى
- ✅ تكامل احترافي مع x64dbg
- ✅ نظام قوائم قياسي
- ✅ نظام تسجيل في الوقت الفعلي
- ⚠️ وظائف أساسية (قابلة للتوسع لاحقاً)

## استكشاف الأخطاء

### إذا لم يظهر البرنامج المساعد:
1. تأكد من أن الملف في المجلد الصحيح: `C:\x64dbg\release\x64\plugins\`
2. تأكد من حجم الملف: يجب أن يكون ~494 KB
3. أعد تشغيل x64dbg بالكامل
4. تحقق من سجل x64dbg للأخطاء

### إذا فشل في التحميل:
1. تأكد من استخدام مجلد x64 (وليس x32)
2. انقر بالزر الأيمن على الملف → Properties → Unblock إذا لزم الأمر
3. تحقق من أن مضاد الفيروسات لا يحجب الملف
4. تأكد من إصدار حديث من x64dbg

## الدعم الفني
إذا واجهت أي مشاكل، تأكد من:
- إصدار x64dbg محدث
- Windows محدث
- لا توجد برامج مكافحة فيروسات تحجب البرنامج
