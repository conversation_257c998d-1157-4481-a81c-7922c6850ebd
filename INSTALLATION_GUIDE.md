# Advanced Protection Plugin v2.0 - دليل التثبيت

## متطلبات النظام
- Windows 10/11 (64-bit)
- x64dbg (أحدث إصدار)
- Visual Studio 2022 Redistributable

## خطوات التثبيت

### 1. نسخ ملف البرنامج المساعد
```
انسخ الملف: build/Release/AdvancedProtectionPlugin.dp64
إلى مجلد: [x64dbg installation]/plugins/
```

### 2. إعادة تشغيل x64dbg
- أغلق x64dbg إذا كان مفتوحاً
- افتح x64dbg مرة أخرى

### 3. التحقق من التثبيت
- في x64dbg، اذهب إلى قائمة "Plugins"
- يجب أن ترى "Advanced Protection v2.0"

## استخدام البرنامج المساعد

### القوائم المتاحة:
1. **Toggle Protection** - تفعيل/إيقاف الحماية
2. **Show Protection UI** - عرض واجهة الحماية
3. **Configuration** - إعدادات البرنامج
4. **About** - معلومات حول البرنامج

### المميزات الرئيسية:
- ✅ حماية من Blue Screen of Death
- ✅ كشف Process Hollowing
- ✅ كشف Code Injection
- ✅ مراقبة الشبكة
- ✅ حماية الذاكرة
- ✅ تحليل السلوك
- ✅ واجهة مستخدم احترافية

## استكشاف الأخطاء

### إذا لم يظهر البرنامج المساعد:
1. تأكد من أن الملف في المجلد الصحيح
2. تأكد من أن x64dbg يدعم plugins
3. تحقق من سجل الأخطاء في x64dbg

### إذا فشل في التحميل:
1. تأكد من تثبيت Visual Studio 2022 Redistributable
2. تحقق من أن Windows Defender لا يحجب الملف
3. تشغيل x64dbg كمدير

## الدعم الفني
إذا واجهت أي مشاكل، تأكد من:
- إصدار x64dbg محدث
- Windows محدث
- لا توجد برامج مكافحة فيروسات تحجب البرنامج
