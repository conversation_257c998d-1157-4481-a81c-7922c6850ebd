#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include "plugin.h"
#include <pluginsdk/_scriptapi_gui.h>
#include <pluginsdk/_scriptapi_misc.h>

// Menu IDs
enum {
    MENU_PROTECTION_TOGGLE = 1,
    MENU_SHOW_UI,
    MENU_CONFIGURATION,
    MENU_ABOUT
};

// External variables from pluginmain.cpp
extern int pluginHandle;
extern HWND hwndDlg;
extern int hMenu;

// Plugin callbacks
static bool cbMenuProtectionToggle(int hMenu) {
    _plugin_logprintf("[AdvancedProtection] Protection toggled\n");
    return true;
}

static bool cbMenuShowUI(int hMenu) {
    _plugin_logprintf("[AdvancedProtection] UI requested\n");
    return true;
}

static bool cbMenuConfiguration(int hMenu) {
    _plugin_logprintf("[AdvancedProtection] Configuration opened\n");
    return true;
}

static bool cbMenuAbout(int hMenu) {
    _plugin_logprintf("[AdvancedProtection] About: %s v%X\n", PLUGIN_NAME, PLUGIN_VERSION);
    return true;
}

// Menu callback handler
void CBMENUENTRY(CBTYPE cbType, PLUG_CB_MENUENTRY* info) {
    switch (info->hEntry) {
        case MENU_PROTECTION_TOGGLE:
            cbMenuProtectionToggle(info->hEntry);
            break;
        case MENU_SHOW_UI:
            cbMenuShowUI(info->hEntry);
            break;
        case MENU_CONFIGURATION:
            cbMenuConfiguration(info->hEntry);
            break;
        case MENU_ABOUT:
            cbMenuAbout(info->hEntry);
            break;
    }
}

bool pluginInit(PLUG_INITSTRUCT* initStruct)
{
    _plugin_logprintf("[AdvancedProtection] Plugin initialized!\n");
    return true;
}

void pluginStop()
{
    _plugin_logprintf("[AdvancedProtection] Plugin stopped!\n");
}

void pluginSetup()
{
    // Add main menu
    _plugin_menuadd(hMenu, "Advanced Protection v2.0");
    
    // Add submenu items
    _plugin_menuaddentry(hMenu, MENU_PROTECTION_TOGGLE, "Toggle Protection");
    _plugin_menuaddentry(hMenu, MENU_SHOW_UI, "Show Protection UI");
    _plugin_menuaddentry(hMenu, MENU_CONFIGURATION, "Configuration");
    _plugin_menuaddseparator(hMenu);
    _plugin_menuaddentry(hMenu, MENU_ABOUT, "About");
    
    // Register menu callback
    _plugin_registercallback(pluginHandle, CB_MENUENTRY, (CBPLUGIN)CBMENUENTRY);
    
    _plugin_logprintf("[AdvancedProtection] Plugin setup complete!\n");
}
