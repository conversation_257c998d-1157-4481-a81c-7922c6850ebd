#include "core/protection_engine.h"
#include "ui/protection_ui.h"
#include <pluginsdk/_plugins.h>
#include <pluginsdk/_scriptapi_gui.h>
#include <pluginsdk/_scriptapi_misc.h>
#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include <sstream>

// Plugin information
#define PLUGIN_NAME "Advanced Protection Plugin v2.0"
#define PLUGIN_VERSION 0x0200

// Global instances
static std::unique_ptr<ProtectionEngine> g_protectionEngine;
static std::unique_ptr<ProtectionUI> g_protectionUI;
static HWND g_hwndDlg = nullptr;
static HWND hwndDlg = nullptr;
static int pluginHandle = 0;
static int hMenu = 0;
static int hMenuDisasm = 0;
static int hMenuDump = 0;
static int hMenuStack = 0;
static bool g_pluginInitialized = false;

// Plugin callbacks
static bool cbMenuProtectionToggle(int hMenu);
static bool cbMenuShowUI(int hMenu);
static bool cbMenuConfiguration(int hMenu);
static bool cbMenuAbout(int hMenu);

// Menu IDs
enum {
    MENU_PROTECTION_TOGGLE = 1,
    MENU_SHOW_UI,
    MENU_CONFIGURATION,
    MENU_ABOUT
};

// Forward declarations
static void InitializeProtectionSystem();
static void ShutdownProtectionSystem() {
    if (g_protectionEngine) {
        g_protectionEngine->StopProtection();
        g_protectionEngine.reset();
    }
    if (g_protectionUI) {
        g_protectionUI.reset();
    }
    g_pluginInitialized = false;
}
static void ShowProtectionUI();
static void OnThreatDetected(const ThreatInfo& threat);
static void OnProcessDetected(const ProcessInfo& process);

/**
 * @brief Plugin initialization
 */
bool pluginInit(PLUG_INITSTRUCT* initStruct) {
    std::cout << "[AdvancedProtection] Initializing " << PLUGIN_NAME << "..." << std::endl;

    try {
        // Initialize protection engine
        g_protectionEngine = std::make_unique<ProtectionEngine>();
        if (!g_protectionEngine->Initialize()) {
            std::cerr << "[AdvancedProtection] Failed to initialize protection engine" << std::endl;
            return false;
        }

        // Set up callbacks
        g_protectionEngine->SetThreatCallback(OnThreatDetected);
        g_protectionEngine->SetProcessCallback(OnProcessDetected);

        // Initialize UI
        g_protectionUI = std::make_unique<ProtectionUI>();
        if (!g_protectionUI->Initialize(GuiGetWindowHandle(), g_protectionEngine.get())) {
            std::cerr << "[AdvancedProtection] Failed to initialize UI" << std::endl;
            // Continue without UI
        }

        g_pluginInitialized = true;
        std::cout << "[AdvancedProtection] Successfully initialized!" << std::endl;
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[AdvancedProtection] Initialization failed: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Plugin setup (called after initialization)
 */
void pluginSetup() {
    std::cout << "[AdvancedProtection] Setting up plugin..." << std::endl;

    // Add menu items
    _plugin_menuaddentry(hMenu, MENU_PROTECTION_TOGGLE, "&Toggle Protection");
    _plugin_menuaddentry(hMenu, MENU_SHOW_UI, "&Show Protection UI");
    _plugin_menuaddentry(hMenu, MENU_CONFIGURATION, "&Configuration");
    _plugin_menuaddseparator(hMenu);
    _plugin_menuaddentry(hMenu, MENU_ABOUT, "&About");

    // Set menu callbacks
    _plugin_menuentrysethotkey(hMenu, MENU_PROTECTION_TOGGLE, "Ctrl+Shift+P");
    _plugin_menuentrysethotkey(hMenu, MENU_SHOW_UI, "Ctrl+Shift+U");

    std::cout << "[AdvancedProtection] Plugin setup complete" << std::endl;
}

/**
 * @brief Plugin stop (called before unloading)
 */
void pluginStop() {
    std::cout << "[AdvancedProtection] Stopping plugin..." << std::endl;
    
    ShutdownProtectionSystem();
    g_pluginInitialized = false;
    
    std::cout << "[AdvancedProtection] Plugin stopped" << std::endl;
}

/**
 * @brief Menu callback handler
 */
void CBMENUENTRY(CBTYPE cbType, PLUG_CB_MENUENTRY* info) {
    switch (info->hEntry) {
        case MENU_PROTECTION_TOGGLE:
            cbMenuProtectionToggle(info->hEntry);
            break;
        case MENU_SHOW_UI:
            cbMenuShowUI(info->hEntry);
            break;
        case MENU_CONFIGURATION:
            cbMenuConfiguration(info->hEntry);
            break;
        case MENU_ABOUT:
            cbMenuAbout(info->hEntry);
            break;
    }
}

/**
 * @brief Toggle protection on/off
 */
static bool cbMenuProtectionToggle(int hMenu) {
    if (!g_protectionEngine) {
        _plugin_logputs("[AdvancedProtection] Protection engine not initialized");
        return false;
    }

    try {
        if (g_protectionEngine->IsProtectionActive()) {
            if (g_protectionEngine->StopProtection()) {
                _plugin_logputs("[AdvancedProtection] Protection stopped");
                GuiAddStatusBarMessage("[AdvancedProtection] Protection DISABLED\n");
            } else {
                _plugin_logputs("[AdvancedProtection] Failed to stop protection");
            }
        } else {
            if (g_protectionEngine->StartProtection()) {
                _plugin_logputs("[AdvancedProtection] Protection started");
                GuiAddStatusBarMessage("[AdvancedProtection] Protection ENABLED\n");
                
                // Show notification
                std::string message = "Advanced Protection is now active!\n";
                message += "Protection Level: " + ProtectionEngine::ProtectionLevelToString(
                    g_protectionEngine->GetConfiguration().level) + "\n";
                message += "Real-time monitoring enabled.";
                
                _plugin_logputs(message.c_str());
            } else {
                _plugin_logputs("[AdvancedProtection] Failed to start protection");
                _plugin_logputs("[AdvancedProtection] Failed to start protection system!");
            }
        }
        
        return true;
    }
    catch (const std::exception& e) {
        std::string error = "Exception in protection toggle: ";
        error += e.what();
        _plugin_logputs(error.c_str());
        _plugin_logputs(error.c_str());
        return false;
    }
}

/**
 * @brief Show protection UI
 */
static bool cbMenuShowUI(int hMenu) {
    if (!g_protectionUI) {
        _plugin_logputs("[AdvancedProtection] UI not initialized");
        _plugin_logputs("[AdvancedProtection] Protection UI is not available");
        return false;
    }

    try {
        ShowProtectionUI();
        return true;
    }
    catch (const std::exception& e) {
        std::string error = "Exception in UI display: ";
        error += e.what();
        _plugin_logputs(error.c_str());
        _plugin_logputs(error.c_str());
        return false;
    }
}

/**
 * @brief Show configuration dialog
 */
static bool cbMenuConfiguration(int hMenu) {
    if (!g_protectionEngine) {
        _plugin_logputs("[AdvancedProtection] Protection engine not initialized");
        return false;
    }

    try {
        // Get current configuration
        const auto& config = g_protectionEngine->GetConfiguration();
        
        // Show configuration dialog (simplified)
        std::string configInfo = "Current Protection Configuration:\n\n";
        configInfo += "Protection Level: " + ProtectionEngine::ProtectionLevelToString(config.level) + "\n";
        configInfo += "Anti-Debugging: " + std::string(config.enableAntiDebugging ? "Enabled" : "Disabled") + "\n";
        configInfo += "Process Hollowing Detection: " + std::string(config.enableProcessHollowingDetection ? "Enabled" : "Disabled") + "\n";
        configInfo += "Code Injection Detection: " + std::string(config.enableCodeInjectionDetection ? "Enabled" : "Disabled") + "\n";
        configInfo += "API Hooking Detection: " + std::string(config.enableAPIHookingDetection ? "Enabled" : "Disabled") + "\n";
        configInfo += "Memory Protection: " + std::string(config.enableMemoryProtection ? "Enabled" : "Disabled") + "\n";
        configInfo += "Network Monitoring: " + std::string(config.enableNetworkMonitoring ? "Enabled" : "Disabled") + "\n";
        configInfo += "Behavior Analysis: " + std::string(config.enableBehaviorAnalysis ? "Enabled" : "Disabled") + "\n";
        configInfo += "Real-time Scanning: " + std::string(config.enableRealTimeScanning ? "Enabled" : "Disabled") + "\n";
        configInfo += "\nResponse Actions:\n";
        configInfo += "Block Threats: " + std::string(config.blockThreats ? "Yes" : "No") + "\n";
        configInfo += "Log Threats: " + std::string(config.logThreats ? "Yes" : "No") + "\n";
        configInfo += "Alert User: " + std::string(config.alertUser ? "Yes" : "No") + "\n";

        _plugin_logputs(configInfo.c_str());
        
        return true;
    }
    catch (const std::exception& e) {
        std::string error = "Exception in configuration display: ";
        error += e.what();
        _plugin_logputs(error.c_str());
        _plugin_logputs(error.c_str());
        return false;
    }
}

/**
 * @brief Show about dialog
 */
static bool cbMenuAbout(int hMenu) {
    std::string aboutText = PLUGIN_NAME;
    aboutText += "\n\nProfessional-grade protection system for x64dbg";
    aboutText += "\nDeveloped by Cybersecurity Professional";
    aboutText += "\n\nFeatures:";
    aboutText += "\n• Advanced API Hooking";
    aboutText += "\n• Real-time Threat Detection";
    aboutText += "\n• Anti-Debugging Protection";
    aboutText += "\n• Process Hollowing Detection";
    aboutText += "\n• Code Injection Detection";
    aboutText += "\n• Memory Protection";
    aboutText += "\n• Network Monitoring";
    aboutText += "\n• Behavioral Analysis";
    aboutText += "\n• Professional UI";
    aboutText += "\n\nVersion: 2.0";
    aboutText += "\nBuild: " + std::string(__DATE__) + " " + std::string(__TIME__);

    _plugin_logputs(aboutText.c_str());
    return true;
}

/**
 * @brief Show protection UI window
 */
static void ShowProtectionUI() {
    if (g_protectionUI) {
        g_protectionUI->Show();
        _plugin_logputs("[AdvancedProtection] Protection UI displayed");
    }
}

/**
 * @brief Threat detection callback
 */
static void OnThreatDetected(const ThreatInfo& threat) {
    // Log threat
    std::string logMessage = "[THREAT DETECTED] ";
    logMessage += ProtectionEngine::ThreatTypeToString(threat.type);
    logMessage += " - " + ProtectionEngine::ThreatSeverityToString(threat.severity);
    logMessage += " - " + threat.description;
    
    _plugin_logputs(logMessage.c_str());

    // Show alert for high severity threats
    if (threat.severity >= ThreatSeverity::HIGH) {
        std::string alertMessage = "High-severity threat detected!\n\n";
        alertMessage += "Type: " + ProtectionEngine::ThreatTypeToString(threat.type) + "\n";
        alertMessage += "Severity: " + ProtectionEngine::ThreatSeverityToString(threat.severity) + "\n";
        alertMessage += "Description: " + threat.description + "\n";
        alertMessage += "Process ID: " + std::to_string(threat.processId) + "\n";
        
        if (threat.address != 0) {
            char addressStr[32];
            sprintf_s(addressStr, "Address: 0x%llX", threat.address);
            alertMessage += std::string(addressStr) + "\n";
        }

        _plugin_logputs(alertMessage.c_str());
        
        // Add to status bar
        std::string statusMsg = "[THREAT] " + ProtectionEngine::ThreatTypeToString(threat.type);
        _plugin_logputs(statusMsg.c_str());
    }
}

/**
 * @brief Process detection callback
 */
static void OnProcessDetected(const ProcessInfo& process) {
    if (process.isSuspicious) {
        std::string logMessage = "[SUSPICIOUS PROCESS] ";
        logMessage += process.processName;
        logMessage += " (PID: " + std::to_string(process.processId) + ")";
        logMessage += " - Suspicion Score: " + std::to_string(process.suspicionScore);
        
        _plugin_logputs(logMessage.c_str());
    }
}

/**
 * @brief Plugin export structure
 */
extern "C" __declspec(dllexport) void pluginit(PLUG_INITSTRUCT* initStruct) {
    initStruct->pluginVersion = PLUGIN_VERSION;
    initStruct->sdkVersion = PLUG_SDKVERSION;
    strcpy_s(initStruct->pluginName, PLUGIN_NAME);
    initStruct->pluginHandle = pluginHandle;
}

extern "C" __declspec(dllexport) bool pluginsetup(PLUG_SETUPSTRUCT* setupStruct) {
    hwndDlg = setupStruct->hwndDlg;
    hMenu = setupStruct->hMenu;
    hMenuDisasm = setupStruct->hMenuDisasm;
    hMenuDump = setupStruct->hMenuDump;
    hMenuStack = setupStruct->hMenuStack;
    
    return pluginInit(nullptr);
}
