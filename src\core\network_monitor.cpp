#include "network_monitor.h"
#include <iostream>
#include <algorithm>
#include <iphlpapi.h>
#include <ws2tcpip.h>

#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "ws2_32.lib")

NetworkMonitor::NetworkMonitor() {
    // Initialize suspicious ports (common malware ports)
    m_suspiciousPorts = {
        1337, 31337, 12345, 54321, 9999, 6666, 6667, 6668, 6669,
        1234, 1243, 3129, 5742, 6711, 6712, 6713, 6776, 7000,
        7300, 7301, 7306, 7307, 7308, 12076, 12223, 23456, 27374,
        29891, 31789, 31790, 31791, 31792, 40421, 40422, 40423,
        40426, 47262, 54283, 61466, 65000
    };

    // Initialize suspicious addresses (known C&C servers, etc.)
    m_suspiciousAddresses = {
        "127.0.0.1", // Localhost connections can be suspicious
        "0.0.0.0"    // Null route
    };
}

NetworkMonitor::~NetworkMonitor() {
    Stop();
}

bool NetworkMonitor::Start() {
    if (m_isRunning) {
        return true;
    }

    try {
        m_isRunning = true;
        m_monitoringThread = std::thread(&NetworkMonitor::MonitoringThread, this);
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[NetworkMonitor] Failed to start: " << e.what() << std::endl;
        m_isRunning = false;
        return false;
    }
}

void NetworkMonitor::Stop() {
    if (!m_isRunning) {
        return;
    }

    m_isRunning = false;
    
    if (m_monitoringThread.joinable()) {
        m_monitoringThread.join();
    }
}

void NetworkMonitor::SetCallback(NetworkCallback callback) {
    m_callback = callback;
}

void NetworkMonitor::AddSuspiciousPort(DWORD port) {
    if (std::find(m_suspiciousPorts.begin(), m_suspiciousPorts.end(), port) == m_suspiciousPorts.end()) {
        m_suspiciousPorts.push_back(port);
    }
}

void NetworkMonitor::AddSuspiciousAddress(const std::string& address) {
    if (std::find(m_suspiciousAddresses.begin(), m_suspiciousAddresses.end(), address) == m_suspiciousAddresses.end()) {
        m_suspiciousAddresses.push_back(address);
    }
}

std::vector<NetworkActivity> NetworkMonitor::GetRecentActivity() const {
    std::lock_guard<std::mutex> lock(m_activityMutex);
    return m_recentActivity;
}

void NetworkMonitor::MonitoringThread() {
    while (m_isRunning) {
        if (!m_monitoringEnabled) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            continue;
        }

        try {
            // Get TCP connections
            DWORD size = 0;
            GetTcpTable(nullptr, &size, TRUE);
            
            if (size > 0) {
                std::vector<BYTE> buffer(size);
                PMIB_TCPTABLE tcpTable = reinterpret_cast<PMIB_TCPTABLE>(buffer.data());
                
                if (GetTcpTable(tcpTable, &size, TRUE) == NO_ERROR) {
                    for (DWORD i = 0; i < tcpTable->dwNumEntries; i++) {
                        NetworkActivity activity;
                        activity.processId = 0; // Process ID not available in basic TCP table
                        activity.localPort = ntohs((u_short)tcpTable->table[i].dwLocalPort);
                        activity.remotePort = ntohs((u_short)tcpTable->table[i].dwRemotePort);
                        activity.protocol = "TCP";
                        
                        // Convert addresses
                        struct in_addr localAddr, remoteAddr;
                        localAddr.S_un.S_addr = tcpTable->table[i].dwLocalAddr;
                        remoteAddr.S_un.S_addr = tcpTable->table[i].dwRemoteAddr;
                        
                        char localStr[INET_ADDRSTRLEN], remoteStr[INET_ADDRSTRLEN];
                        inet_ntop(AF_INET, &localAddr, localStr, INET_ADDRSTRLEN);
                        inet_ntop(AF_INET, &remoteAddr, remoteStr, INET_ADDRSTRLEN);
                        
                        activity.localAddress = localStr;
                        activity.remoteAddress = remoteStr;
                        
                        GetSystemTime(&activity.timestamp);
                        
                        // Analyze for suspicious activity
                        activity.isSuspicious = AnalyzeConnection(activity);
                        
                        // Store activity
                        {
                            std::lock_guard<std::mutex> lock(m_activityMutex);
                            m_recentActivity.push_back(activity);
                            if (m_recentActivity.size() > MAX_RECENT_ACTIVITIES) {
                                m_recentActivity.erase(m_recentActivity.begin());
                            }
                        }
                        
                        // Update statistics
                        m_totalConnections++;
                        if (activity.isSuspicious) {
                            m_suspiciousConnections++;
                        }
                        
                        // Notify callback
                        if (m_callback && activity.isSuspicious) {
                            m_callback(activity);
                        }
                    }
                }
            }
        }
        catch (const std::exception& e) {
            std::cerr << "[NetworkMonitor] Error in monitoring thread: " << e.what() << std::endl;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    }
}

bool NetworkMonitor::AnalyzeConnection(const NetworkActivity& activity) {
    // Check suspicious ports
    if (IsSuspiciousPort(activity.localPort) || IsSuspiciousPort(activity.remotePort)) {
        return true;
    }
    
    // Check suspicious addresses
    if (IsSuspiciousAddress(activity.localAddress) || IsSuspiciousAddress(activity.remoteAddress)) {
        return true;
    }
    
    return false;
}

bool NetworkMonitor::IsSuspiciousPort(DWORD port) const {
    return std::find(m_suspiciousPorts.begin(), m_suspiciousPorts.end(), port) != m_suspiciousPorts.end();
}

bool NetworkMonitor::IsSuspiciousAddress(const std::string& address) const {
    return std::find(m_suspiciousAddresses.begin(), m_suspiciousAddresses.end(), address) != m_suspiciousAddresses.end();
}
