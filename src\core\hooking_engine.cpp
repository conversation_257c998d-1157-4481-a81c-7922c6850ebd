#include "hooking_engine.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <psapi.h>
#include <tlhelp32.h>

// Detours library for professional hooking
#pragma comment(lib, "detours.lib")
#include <Windows.h>
// Note: Detours will be included via CMake target linking

// Disassembly engine
#include <capstone/capstone.h>
#pragma comment(lib, "capstone.lib")

HookingEngine::HookingEngine() 
    : m_initialized(false), m_enableGlobalLogging(true), m_enableGlobalCallStack(false),
      m_exceptionHandler(nullptr), m_isInstalling(false) {
    memset(&m_statistics, 0, sizeof(m_statistics));
}

HookingEngine::~HookingEngine() {
    Shutdown();
}

bool HookingEngine::Initialize() {
    if (m_initialized) {
        return true;
    }

    try {
        // Initialize Detours
        DetourTransactionBegin();
        DetourUpdateThread(GetCurrentThread());
        
        if (DetourTransactionCommit() != NO_ERROR) {
            std::cerr << "[HookingEngine] Failed to initialize Detours" << std::endl;
            return false;
        }

        // Install vectored exception handler for advanced hooking techniques
        m_exceptionHandler = AddVectoredExceptionHandler(1, VectoredExceptionHandler);
        if (!m_exceptionHandler) {
            std::cerr << "[HookingEngine] Failed to install exception handler" << std::endl;
            return false;
        }

        // Initialize Capstone disassembly engine
        csh handle;
        if (cs_open(CS_ARCH_X86, CS_MODE_64, &handle) != CS_ERR_OK) {
            std::cerr << "[HookingEngine] Failed to initialize disassembly engine" << std::endl;
            return false;
        }
        cs_close(&handle);

        m_initialized = true;
        std::cout << "[HookingEngine] Successfully initialized advanced hooking engine" << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[HookingEngine] Initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void HookingEngine::Shutdown() {
    if (!m_initialized) {
        return;
    }

    // Remove all hooks
    RemoveAllHooks();

    // Remove exception handler
    if (m_exceptionHandler) {
        RemoveVectoredExceptionHandler(m_exceptionHandler);
        m_exceptionHandler = nullptr;
    }

    // Free allocated memory
    {
        std::lock_guard<std::mutex> lock(m_memoryMutex);
        for (void* memory : m_allocatedMemory) {
            VirtualFree(memory, 0, MEM_RELEASE);
        }
        m_allocatedMemory.clear();
    }

    m_initialized = false;
    std::cout << "[HookingEngine] Shutdown complete" << std::endl;
}

bool HookingEngine::InstallHook(const std::string& name, const std::string& moduleName, 
                               const std::string& functionName, HookCallback callback,
                               const HookConfig& config) {
    if (!m_initialized) {
        std::cerr << "[HookingEngine] Engine not initialized" << std::endl;
        return false;
    }

    // Get function address
    uintptr_t targetAddress = GetFunctionAddress(moduleName, functionName);
    if (targetAddress == 0) {
        std::cerr << "[HookingEngine] Function not found: " << moduleName << "::" << functionName << std::endl;
        return false;
    }

    return InstallHook(name, targetAddress, callback, config);
}

bool HookingEngine::InstallHook(const std::string& name, uintptr_t targetAddress, 
                               HookCallback callback, const HookConfig& config) {
    if (!ValidateHookParameters(name, targetAddress, callback, config)) {
        return false;
    }

    std::lock_guard<std::mutex> installLock(m_installMutex);
    std::lock_guard<std::mutex> hooksLock(m_hooksMutex);

    // Check if hook already exists
    if (m_hooks.find(name) != m_hooks.end()) {
        std::cerr << "[HookingEngine] Hook already exists: " << name << std::endl;
        return false;
    }

    // Create hook object
    auto hook = std::make_shared<Hook>();
    hook->name = name;
    hook->originalAddress = targetAddress;
    hook->callback = callback;
    hook->config = config;
    hook->type = config.type;
    hook->status = HookStatus::INSTALLING;
    GetSystemTime(&hook->installTime);

    // Install hook based on type
    bool success = false;
    switch (config.type) {
        case HookType::DETOUR_HOOK:
            success = InstallDetourHook(hook);
            break;
        case HookType::INLINE_HOOK:
            success = InstallInlineHook(hook);
            break;
        case HookType::IAT_HOOK:
            success = InstallIATHook(hook);
            break;
        case HookType::EAT_HOOK:
            success = InstallEATHook(hook);
            break;
        case HookType::HARDWARE_BREAKPOINT:
            success = InstallHardwareBreakpoint(hook);
            break;
        case HookType::PAGE_GUARD:
            success = InstallPageGuardHook(hook);
            break;
        default:
            std::cerr << "[HookingEngine] Unsupported hook type" << std::endl;
            return false;
    }

    if (success) {
        hook->status = HookStatus::INSTALLED;
        hook->isEnabled = true;
        m_hooks[name] = hook;
        
        std::cout << "[HookingEngine] Successfully installed hook: " << name 
                  << " at 0x" << std::hex << targetAddress << std::endl;
        
        UpdateStatistics();
        
        if (m_hookCallback) {
            HookInfo info;
            info.apiName = hook->name;
            info.originalAddress = hook->originalAddress;
            info.hookAddress = hook->hookAddress;
            info.originalBytes = hook->originalBytes;
            info.hookBytes = hook->hookBytes;
            info.isActive = hook->isEnabled;
            info.installTime = hook->installTime;
            m_hookCallback(info);
        }
        
        return true;
    } else {
        hook->status = HookStatus::FAILED;
        std::cerr << "[HookingEngine] Failed to install hook: " << name << std::endl;
        return false;
    }
}

bool HookingEngine::InstallDetourHook(std::shared_ptr<Hook> hook) {
    try {
        // Create trampoline function
        if (!CreateTrampoline(hook)) {
            std::cerr << "[HookingEngine] Failed to create trampoline for: " << hook->name << std::endl;
            return false;
        }

        // Begin Detours transaction
        DetourTransactionBegin();
        DetourUpdateThread(GetCurrentThread());

        // Attach the detour
        PVOID originalFunction = reinterpret_cast<PVOID>(hook->originalAddress);
        PVOID hookFunction = reinterpret_cast<PVOID>(hook->trampolineAddress);
        
        if (DetourAttach(&originalFunction, hookFunction) != NO_ERROR) {
            DetourTransactionAbort();
            std::cerr << "[HookingEngine] DetourAttach failed for: " << hook->name << std::endl;
            return false;
        }

        // Commit the transaction
        if (DetourTransactionCommit() != NO_ERROR) {
            std::cerr << "[HookingEngine] DetourTransactionCommit failed for: " << hook->name << std::endl;
            return false;
        }

        hook->hookAddress = reinterpret_cast<uintptr_t>(hookFunction);
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[HookingEngine] Exception in InstallDetourHook: " << e.what() << std::endl;
        return false;
    }
}

bool HookingEngine::InstallInlineHook(std::shared_ptr<Hook> hook) {
    try {
        // Read original bytes
        SIZE_T bytesRead;
        hook->originalBytes.resize(32); // Read more bytes for safety
        if (!ReadProcessMemory(GetCurrentProcess(), 
                              reinterpret_cast<LPCVOID>(hook->originalAddress),
                              hook->originalBytes.data(), 
                              hook->originalBytes.size(), 
                              &bytesRead)) {
            std::cerr << "[HookingEngine] Failed to read original bytes" << std::endl;
            return false;
        }

        // Calculate minimum instruction length to overwrite
        size_t minLength = 5; // JMP instruction length
        size_t actualLength = 0;
        
        csh handle;
        cs_insn* insn;
        
        if (cs_open(CS_ARCH_X86, CS_MODE_64, &handle) == CS_ERR_OK) {
            size_t count = cs_disasm(handle, hook->originalBytes.data(), 
                                   hook->originalBytes.size(), 
                                   hook->originalAddress, 0, &insn);
            
            for (size_t i = 0; i < count && actualLength < minLength; i++) {
                actualLength += insn[i].size;
            }
            
            cs_free(insn, count);
            cs_close(&handle);
        } else {
            actualLength = minLength; // Fallback
        }

        // Create trampoline
        if (!CreateTrampoline(hook)) {
            return false;
        }

        // Change memory protection
        DWORD oldProtect;
        if (!VirtualProtect(reinterpret_cast<LPVOID>(hook->originalAddress), 
                           actualLength, PAGE_EXECUTE_READWRITE, &oldProtect)) {
            std::cerr << "[HookingEngine] Failed to change memory protection" << std::endl;
            return false;
        }

        // Write jump instruction
        if (!WriteJumpInstruction(hook->originalAddress, hook->trampolineAddress)) {
            VirtualProtect(reinterpret_cast<LPVOID>(hook->originalAddress), 
                          actualLength, oldProtect, &oldProtect);
            return false;
        }

        // Restore memory protection
        VirtualProtect(reinterpret_cast<LPVOID>(hook->originalAddress), 
                      actualLength, oldProtect, &oldProtect);

        hook->hookAddress = hook->trampolineAddress;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[HookingEngine] Exception in InstallInlineHook: " << e.what() << std::endl;
        return false;
    }
}

bool HookingEngine::CreateTrampoline(std::shared_ptr<Hook> hook) {
    // Allocate executable memory for trampoline
    void* trampolineMemory = AllocateExecutableMemory(hook->config.trampolineSize);
    if (!trampolineMemory) {
        std::cerr << "[HookingEngine] Failed to allocate trampoline memory" << std::endl;
        return false;
    }

    hook->trampolineAddress = reinterpret_cast<uintptr_t>(trampolineMemory);

    // Create trampoline code that calls our callback and then original function
    // This is a simplified implementation - in production, you'd need more sophisticated
    // assembly code generation based on the calling convention and parameters
    
    uint8_t* code = static_cast<uint8_t*>(trampolineMemory);
    size_t offset = 0;

    // Save registers (simplified)
    code[offset++] = 0x50; // push rax
    code[offset++] = 0x51; // push rcx
    code[offset++] = 0x52; // push rdx
    code[offset++] = 0x53; // push rbx

    // Call our hook callback (simplified - would need proper parameter passing)
    // This is where you'd implement the actual callback invocation

    // Restore registers
    code[offset++] = 0x5B; // pop rbx
    code[offset++] = 0x5A; // pop rdx
    code[offset++] = 0x59; // pop rcx
    code[offset++] = 0x58; // pop rax

    // Jump to original function (or execute original instructions)
    // This would contain the original instructions that were overwritten
    // followed by a jump back to the original function

    return true;
}

void* HookingEngine::AllocateExecutableMemory(size_t size) {
    void* memory = VirtualAlloc(nullptr, size, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
    if (memory) {
        std::lock_guard<std::mutex> lock(m_memoryMutex);
        m_allocatedMemory.push_back(memory);
    }
    return memory;
}

bool HookingEngine::WriteJumpInstruction(uintptr_t from, uintptr_t to) {
    // Calculate relative offset
    int64_t offset = static_cast<int64_t>(to) - static_cast<int64_t>(from) - 5;
    
    // Check if offset fits in 32-bit signed integer
    if (offset > INT32_MAX || offset < INT32_MIN) {
        std::cerr << "[HookingEngine] Jump offset too large for relative jump" << std::endl;
        return false;
    }

    // Write JMP instruction (E9 xx xx xx xx)
    uint8_t jumpInstruction[5];
    jumpInstruction[0] = 0xE9; // JMP rel32
    *reinterpret_cast<int32_t*>(&jumpInstruction[1]) = static_cast<int32_t>(offset);

    // Write to memory
    SIZE_T bytesWritten;
    return WriteProcessMemory(GetCurrentProcess(), 
                             reinterpret_cast<LPVOID>(from),
                             jumpInstruction, 
                             sizeof(jumpInstruction), 
                             &bytesWritten) && bytesWritten == sizeof(jumpInstruction);
}

bool HookingEngine::ValidateHookParameters(const std::string& name, uintptr_t targetAddress, 
                                          HookCallback callback, const HookConfig& config) {
    if (name.empty()) {
        std::cerr << "[HookingEngine] Hook name cannot be empty" << std::endl;
        return false;
    }

    if (targetAddress == 0) {
        std::cerr << "[HookingEngine] Invalid target address" << std::endl;
        return false;
    }

    if (!callback) {
        std::cerr << "[HookingEngine] Hook callback cannot be null" << std::endl;
        return false;
    }

    if (!IsAddressHookable(targetAddress)) {
        std::cerr << "[HookingEngine] Address is not hookable: 0x" << std::hex << targetAddress << std::endl;
        return false;
    }

    return true;
}

bool HookingEngine::IsAddressHookable(uintptr_t address) {
    // Check if memory is readable and executable
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi)) == 0) {
        return false;
    }

    return (mbi.State == MEM_COMMIT) && 
           (mbi.Protect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE | PAGE_EXECUTE_WRITECOPY));
}

uintptr_t HookingEngine::GetFunctionAddress(const std::string& moduleName, const std::string& functionName) {
    HMODULE hModule = GetModuleHandleA(moduleName.c_str());
    if (!hModule) {
        hModule = LoadLibraryA(moduleName.c_str());
        if (!hModule) {
            return 0;
        }
    }

    FARPROC proc = GetProcAddress(hModule, functionName.c_str());
    return reinterpret_cast<uintptr_t>(proc);
}

void HookingEngine::UpdateStatistics() {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    m_statistics.totalHooks = m_hooks.size();
    m_statistics.activeHooks = 0;
    m_statistics.failedHooks = 0;
    m_statistics.totalCalls = 0;
    
    m_statistics.hooksByType.clear();
    m_statistics.hooksByModule.clear();

    for (const auto& pair : m_hooks) {
        const auto& hook = pair.second;
        
        if (hook->isEnabled && hook->status == HookStatus::INSTALLED) {
            m_statistics.activeHooks++;
        }
        
        if (hook->status == HookStatus::FAILED) {
            m_statistics.failedHooks++;
        }
        
        m_statistics.totalCalls += hook->callCount;
        m_statistics.hooksByType[hook->type]++;
        
        if (!hook->moduleName.empty()) {
            m_statistics.hooksByModule[hook->moduleName]++;
        }
    }

    if (m_statistics.totalHooks > 0) {
        m_statistics.averageCallsPerHook = static_cast<double>(m_statistics.totalCalls) / m_statistics.totalHooks;
    }
}

LONG WINAPI HookingEngine::VectoredExceptionHandler(PEXCEPTION_POINTERS exceptionInfo) {
    // Handle exceptions for advanced hooking techniques like hardware breakpoints
    // and page guard hooks
    
    DWORD exceptionCode = exceptionInfo->ExceptionRecord->ExceptionCode;
    
    switch (exceptionCode) {
        case EXCEPTION_SINGLE_STEP:
            // Handle hardware breakpoint hits
            break;
            
        case EXCEPTION_GUARD_PAGE:
            // Handle page guard exceptions
            break;
            
        default:
            break;
    }
    
    return EXCEPTION_CONTINUE_SEARCH;
}
