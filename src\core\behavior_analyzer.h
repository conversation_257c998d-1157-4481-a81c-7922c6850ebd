#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <mutex>
#include <atomic>
#include <unordered_map>
#include <queue>

// Behavior pattern information
struct BehaviorPattern {
    std::string name;
    std::string description;
    float severity;
    std::vector<std::string> indicators;
    SYSTEMTIME detectionTime;
    bool isActive;
};

// Process behavior information
struct ProcessBehavior {
    DWORD processId;
    std::string processName;
    std::string imagePath;
    std::vector<std::string> apiCalls;
    std::vector<std::string> fileAccesses;
    std::vector<std::string> registryAccesses;
    std::vector<std::string> networkConnections;
    float suspicionScore;
    SYSTEMTIME startTime;
    bool isSuspicious;
};

// Behavior event
struct BehaviorEvent {
    DWORD processId;
    std::string eventType;
    std::string description;
    std::string details;
    float severity;
    SYSTEMTIME timestamp;
};

// Behavior analysis callback
using BehaviorCallback = std::function<void(const BehaviorEvent&)>;

class BehaviorAnalyzer {
public:
    BehaviorAnalyzer();
    ~BehaviorAnalyzer();

    // Control methods
    bool Start();
    void Stop();
    bool IsRunning() const { return m_isRunning; }

    // Analysis methods
    void AnalyzeProcess(DWORD processId);
    void AnalyzeApiCall(DWORD processId, const std::string& apiName, const std::vector<std::string>& parameters);
    void AnalyzeFileAccess(DWORD processId, const std::string& filePath, const std::string& operation);
    void AnalyzeRegistryAccess(DWORD processId, const std::string& keyPath, const std::string& operation);
    void AnalyzeNetworkActivity(DWORD processId, const std::string& address, DWORD port, const std::string& protocol);

    // Pattern detection
    bool DetectCodeInjection(DWORD processId);
    bool DetectProcessHollowing(DWORD processId);
    bool DetectKeylogging(DWORD processId);
    bool DetectScreenCapture(DWORD processId);
    bool DetectCryptocurrency(DWORD processId);
    bool DetectRansomware(DWORD processId);

    // Configuration
    void SetCallback(BehaviorCallback callback);
    void SetSuspicionThreshold(float threshold) { m_suspicionThreshold = threshold; }
    void EnableRealTimeAnalysis(bool enabled) { m_realTimeAnalysis = enabled; }

    // Statistics
    size_t GetAnalyzedProcessesCount() const;
    size_t GetSuspiciousProcessesCount() const;
    size_t GetDetectedPatternsCount() const;
    std::vector<ProcessBehavior> GetSuspiciousProcesses() const;
    std::vector<BehaviorPattern> GetDetectedPatterns() const;
    std::vector<BehaviorEvent> GetRecentEvents() const;

private:
    // Analysis methods
    void UpdateProcessBehavior(DWORD processId, const BehaviorEvent& event);
    float CalculateSuspicionScore(const ProcessBehavior& behavior);
    bool MatchesPattern(const ProcessBehavior& behavior, const BehaviorPattern& pattern);
    
    // Pattern definitions
    void InitializePatterns();
    BehaviorPattern CreateCodeInjectionPattern();
    BehaviorPattern CreateProcessHollowingPattern();
    BehaviorPattern CreateKeyloggingPattern();
    BehaviorPattern CreateScreenCapturePattern();
    BehaviorPattern CreateCryptocurrencyPattern();
    BehaviorPattern CreateRansomwarePattern();

    // Utility methods
    bool IsSystemProcess(DWORD processId);
    bool IsWhitelistedProcess(const std::string& processName);
    std::string GetProcessName(DWORD processId);
    std::string GetProcessPath(DWORD processId);

    // Member variables
    std::atomic<bool> m_isRunning{false};
    std::atomic<bool> m_realTimeAnalysis{true};
    std::atomic<float> m_suspicionThreshold{0.7f};

    // Process tracking
    mutable std::mutex m_processesMutex;
    std::unordered_map<DWORD, ProcessBehavior> m_processMap;

    // Pattern storage
    mutable std::mutex m_patternsMutex;
    std::vector<BehaviorPattern> m_knownPatterns;
    std::vector<BehaviorPattern> m_detectedPatterns;

    // Event tracking
    mutable std::mutex m_eventsMutex;
    std::queue<BehaviorEvent> m_recentEvents;
    static const size_t MAX_RECENT_EVENTS = 1000;

    // Callback
    BehaviorCallback m_callback;

    // Statistics
    std::atomic<size_t> m_analyzedProcesses{0};
    std::atomic<size_t> m_suspiciousProcesses{0};
    std::atomic<size_t> m_detectedPatternsCount{0};
};
