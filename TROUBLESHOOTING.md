# استكشاف أخطاء البرنامج المساعد - Advanced Protection Plugin v2.0

## المشكلة: البرنامج المساعد لا يظهر في x64dbg

### الخطوات التشخيصية:

## 1. التحقق من مكان الملف
```
تأكد من أن الملف موجود في:
[مجلد x64dbg]/plugins/AdvancedProtectionPlugin.dp64

مثال:
C:\Program Files\x64dbg\plugins\AdvancedProtectionPlugin.dp64
```

## 2. التحقق من صحة الملف
```
حجم الملف يجب أن يكون: ~9.16 MB
امتداد الملف: .dp64 (وليس .dll)
```

## 3. فحص سجل x64dbg
1. افتح x64dbg
2. اذه<PERSON> إلى نافذة "Log"
3. ابحث عن رسائل خطأ تتعلق بـ "AdvancedProtectionPlugin"

## 4. التحقق من الأذونات
```
تأكد من أن:
- x64dbg يعمل كمدير (Run as Administrator)
- مجلد plugins قابل للكتابة
- Windows Defender لا يحجب الملف
```

## 5. التحقق من التبعيات
تأكد من تثبيت:
- Visual Studio 2022 Redistributable (x64)
- .NET Framework 4.8 أو أحدث

## 6. اختبار بسيط
1. احذف الملف من مجلد plugins
2. أعد تشغيل x64dbg
3. أضف الملف مرة أخرى
4. أعد تشغيل x64dbg

## 7. فحص التوافق
```
تأكد من:
- إصدار x64dbg حديث (2023 أو أحدث)
- Windows 10/11 (64-bit)
- لا توجد برامج مكافحة فيروسات تحجب الملف
```

## 8. اختبار متقدم
إذا لم تنجح الخطوات السابقة:

### أ. فحص Event Viewer
1. افتح Event Viewer
2. اذهب إلى Windows Logs > Application
3. ابحث عن أخطاء تتعلق بـ x64dbg

### ب. اختبار dependency
```cmd
# في Command Prompt
cd "C:\Program Files\x64dbg\plugins"
dumpbin /dependents AdvancedProtectionPlugin.dp64
```

### ج. فحص الذاكرة
```
تأكد من وجود ذاكرة كافية (4GB+ RAM)
أغلق البرامج غير الضرورية
```

## 9. حلول بديلة

### الحل الأول: إعادة البناء
```bash
# في مجلد المشروع
cmake --build build --config Release --clean-first
```

### الحل الثاني: نسخ يدوي
```
1. انسخ جميع ملفات DLL المطلوبة إلى مجلد x64dbg
2. تأكد من وجود:
   - capstone.dll
   - detours.dll (إن وجد)
```

### الحل الثالث: تشغيل تشخيصي
```cmd
# تشغيل x64dbg من Command Line مع verbose logging
x64dbg.exe -v
```

## 10. معلومات إضافية للدعم

إذا استمرت المشكلة، اجمع المعلومات التالية:
- إصدار Windows
- إصدار x64dbg
- رسائل الخطأ من Log
- حجم ملف البرنامج المساعد
- مكان التثبيت الدقيق

## الأخطاء الشائعة وحلولها

### خطأ: "Plugin failed to load"
**الحل**: تأكد من تثبيت Visual C++ Redistributable

### خطأ: "Invalid plugin format"
**الحل**: تأكد من أن الملف .dp64 وليس .dll

### خطأ: "Access denied"
**الحل**: تشغيل x64dbg كمدير

### خطأ: "Missing dependencies"
**الحل**: نسخ مكتبات DLL المطلوبة

---

**ملاحظة**: إذا لم تحل هذه الخطوات المشكلة، قد تحتاج إلى إعادة بناء البرنامج المساعد أو فحص إعدادات النظام.
