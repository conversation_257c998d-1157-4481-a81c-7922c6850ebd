#include "protection_ui.h"
#include <imgui.h>
#include <imgui_impl_win32.h>
#include <imgui_impl_dx11.h>
#include <d3d11.h>
#include <iostream>
#include <sstream>
#include <iomanip>

// DirectX 11 globals
static ID3D11Device* g_pd3dDevice = nullptr;
static ID3D11DeviceContext* g_pd3dDeviceContext = nullptr;
static IDXGISwapChain* g_pSwapChain = nullptr;
static ID3D11RenderTargetView* g_mainRenderTargetView = nullptr;

// Forward declarations
bool CreateDeviceD3D(HWND hWnd);
void CleanupDeviceD3D();
void CreateRenderTarget();
void CleanupRenderTarget();
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

ProtectionUI::ProtectionUI() 
    : m_initialized(false), m_visible(false), m_hwnd(nullptr), m_engine(nullptr),
      m_context(nullptr), m_selectedThreat(-1), m_selectedProcess(-1),
      m_autoScroll(true), m_lastUpdateTime(0.0f), m_frameTime(0.0f), m_fps(0.0f) {
    
    // Initialize configuration with cyber theme
    m_config.theme = UITheme::DARK_CYBER;
    m_config.fontSize = 16.0f;
    m_config.enableAnimations = true;
    m_config.enableNotifications = true;
    m_config.enableTooltips = true;
    
    // Initialize window state
    m_windowState.showMainWindow = true;
    m_windowState.showDashboard = true;
    m_windowState.showThreatMonitor = true;
    m_windowState.showProcessMonitor = true;
    
    // Initialize chart data
    m_threatChart.title = "Threat Detection Rate";
    m_threatChart.unit = "threats/min";
    m_threatChart.maxPoints = 60;
    
    m_cpuChart.title = "CPU Usage";
    m_cpuChart.unit = "%";
    m_cpuChart.maxPoints = 60;
    
    m_memoryChart.title = "Memory Usage";
    m_memoryChart.unit = "MB";
    m_memoryChart.maxPoints = 60;
    
    m_networkChart.title = "Network Activity";
    m_networkChart.unit = "KB/s";
    m_networkChart.maxPoints = 60;
}

ProtectionUI::~ProtectionUI() {
    Shutdown();
}

bool ProtectionUI::Initialize(HWND hwnd, ProtectionEngine* engine) {
    if (m_initialized) {
        return true;
    }

    try {
        std::cout << "[ProtectionUI] Initializing professional UI..." << std::endl;

        m_hwnd = hwnd;
        m_engine = engine;

        // Setup Dear ImGui context
        IMGUI_CHECKVERSION();
        m_context = ImGui::CreateContext();
        ImGuiIO& io = ImGui::GetIO();
        io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
        // Note: Docking and Viewports may not be available in this ImGui version
        // io.ConfigFlags |= ImGuiConfigFlags_DockingEnable;

        // if (m_config.enableViewports) {
        //     io.ConfigFlags |= ImGuiConfigFlags_ViewportsEnable;
        // }

        // Setup Dear ImGui style
        ApplyTheme();

        // Setup Platform/Renderer backends
        if (!ImGui_ImplWin32_Init(hwnd)) {
            std::cerr << "[ProtectionUI] Failed to initialize Win32 backend" << std::endl;
            return false;
        }

        // Initialize DirectX 11
        if (!CreateDeviceD3D(hwnd)) {
            std::cerr << "[ProtectionUI] Failed to create DirectX device" << std::endl;
            ImGui_ImplWin32_Shutdown();
            return false;
        }

        if (!ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext)) {
            std::cerr << "[ProtectionUI] Failed to initialize DirectX 11 backend" << std::endl;
            CleanupDeviceD3D();
            ImGui_ImplWin32_Shutdown();
            return false;
        }

        // Load fonts
        LoadFonts();

        m_initialized = true;
        std::cout << "[ProtectionUI] Successfully initialized professional UI!" << std::endl;
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[ProtectionUI] Initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void ProtectionUI::Shutdown() {
    if (!m_initialized) {
        return;
    }

    std::cout << "[ProtectionUI] Shutting down UI..." << std::endl;

    // Cleanup ImGui
    if (m_context) {
        ImGui_ImplDX11_Shutdown();
        ImGui_ImplWin32_Shutdown();
        ImGui::DestroyContext(m_context);
        m_context = nullptr;
    }

    // Cleanup DirectX
    CleanupDeviceD3D();

    m_initialized = false;
    std::cout << "[ProtectionUI] UI shutdown complete" << std::endl;
}

void ProtectionUI::NewFrame() {
    if (!m_initialized) {
        return;
    }

    // Start the Dear ImGui frame
    ImGui_ImplDX11_NewFrame();
    ImGui_ImplWin32_NewFrame();
    ImGui::NewFrame();

    // Update performance metrics
    UpdatePerformanceMetrics();
}

void ProtectionUI::Render() {
    if (!m_initialized || !m_visible) {
        return;
    }

    // Update data from engine
    UpdateThreatData();
    UpdateProcessData();
    UpdateStatistics();

    // Setup docking space
    SetupDockSpace();

    // Render main windows
    if (m_windowState.showMainWindow) {
        RenderMainMenuBar();
    }

    if (m_windowState.showDashboard) {
        RenderDashboard();
    }

    if (m_windowState.showThreatMonitor) {
        RenderThreatMonitor();
    }

    if (m_windowState.showProcessMonitor) {
        RenderProcessMonitor();
    }

    if (m_windowState.showNetworkMonitor) {
        RenderNetworkMonitor();
    }

    if (m_windowState.showConfiguration) {
        RenderConfiguration();
    }

    if (m_windowState.showStatistics) {
        RenderStatistics();
    }

    if (m_windowState.showLogs) {
        RenderLogs();
    }

    if (m_windowState.showAbout) {
        RenderAbout();
    }

    // Render dialogs
    if (m_showConfigDialog) {
        RenderConfigDialog();
    }

    if (m_showAboutDialog) {
        RenderAboutDialog();
    }

    if (m_showThreatDetailsDialog) {
        RenderThreatDetailsDialog();
    }

    if (m_showProcessDetailsDialog) {
        RenderProcessDetailsDialog();
    }

    // Render notifications
    RenderNotifications();

    // Render performance overlay if enabled
    if (ImGui::GetIO().KeyCtrl && ImGui::IsKeyPressed(ImGuiKey_F1)) {
        RenderPerformanceOverlay();
    }
}

void ProtectionUI::EndFrame() {
    if (!m_initialized) {
        return;
    }

    // Rendering
    ImGui::Render();
    
    const float clear_color[4] = { 
        m_config.colors.background[0], 
        m_config.colors.background[1], 
        m_config.colors.background[2], 
        m_config.colors.background[3] 
    };
    
    g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, nullptr);
    g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color);
    ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

    // Update and Render additional Platform Windows
    // Note: Platform windows not supported in this ImGui version
    // ImGuiIO& io = ImGui::GetIO();
    // if (io.ConfigFlags & ImGuiConfigFlags_ViewportsEnable) {
    //     ImGui::UpdatePlatformWindows();
    //     ImGui::RenderPlatformWindowsDefault();
    // }

    g_pSwapChain->Present(1, 0); // Present with vsync
}

void ProtectionUI::Show() {
    m_visible = true;
    if (m_hwnd) {
        ShowWindow(m_hwnd, SW_SHOW);
        SetForegroundWindow(m_hwnd);
    }
}

void ProtectionUI::Hide() {
    m_visible = false;
    if (m_hwnd) {
        ShowWindow(m_hwnd, SW_HIDE);
    }
}

void ProtectionUI::ApplyTheme() {
    ImGuiStyle& style = ImGui::GetStyle();
    
    // Set colors based on theme
    switch (m_config.theme) {
        case UITheme::DARK_CYBER:
            ApplyCyberTheme();
            break;
        case UITheme::DARK_PROFESSIONAL:
            ApplyProfessionalTheme();
            break;
        case UITheme::SECURITY_BLUE:
            ApplySecurityTheme();
            break;
        default:
            ApplyCyberTheme();
            break;
    }

    // Apply styling
    style.WindowRounding = m_config.windowRounding;
    style.FrameRounding = m_config.frameRounding;
    style.ScrollbarRounding = m_config.scrollbarRounding;
    style.GrabRounding = m_config.grabRounding;
    style.TabRounding = m_config.tabRounding;
    
    style.WindowPadding = ImVec2(12, 12);
    style.FramePadding = ImVec2(8, 4);
    style.ItemSpacing = ImVec2(8, 4);
    style.ItemInnerSpacing = ImVec2(4, 4);
    style.IndentSpacing = 20.0f;
    style.ScrollbarSize = 16.0f;
    style.GrabMinSize = 12.0f;
    
    style.WindowBorderSize = 1.0f;
    style.ChildBorderSize = 1.0f;
    style.PopupBorderSize = 1.0f;
    style.FrameBorderSize = 0.0f;
    style.TabBorderSize = 0.0f;
}

void ProtectionUI::SetCyberTheme() {
    ImVec4* colors = ImGui::GetStyle().Colors;
    
    // Dark cyber theme with neon accents
    colors[ImGuiCol_Text] = ImVec4(0.95f, 0.95f, 0.95f, 1.0f);
    colors[ImGuiCol_TextDisabled] = ImVec4(0.50f, 0.50f, 0.50f, 1.0f);
    colors[ImGuiCol_WindowBg] = ImVec4(0.08f, 0.08f, 0.10f, 1.0f);
    colors[ImGuiCol_ChildBg] = ImVec4(0.10f, 0.10f, 0.12f, 1.0f);
    colors[ImGuiCol_PopupBg] = ImVec4(0.08f, 0.08f, 0.10f, 1.0f);
    colors[ImGuiCol_Border] = ImVec4(0.26f, 0.59f, 0.98f, 0.3f);
    colors[ImGuiCol_BorderShadow] = ImVec4(0.00f, 0.00f, 0.00f, 0.0f);
    colors[ImGuiCol_FrameBg] = ImVec4(0.12f, 0.12f, 0.15f, 1.0f);
    colors[ImGuiCol_FrameBgHovered] = ImVec4(0.15f, 0.15f, 0.18f, 1.0f);
    colors[ImGuiCol_FrameBgActive] = ImVec4(0.18f, 0.18f, 0.22f, 1.0f);
    colors[ImGuiCol_TitleBg] = ImVec4(0.06f, 0.06f, 0.08f, 1.0f);
    colors[ImGuiCol_TitleBgActive] = ImVec4(0.26f, 0.59f, 0.98f, 1.0f);
    colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.06f, 0.06f, 0.08f, 0.75f);
    colors[ImGuiCol_MenuBarBg] = ImVec4(0.10f, 0.10f, 0.12f, 1.0f);
    colors[ImGuiCol_ScrollbarBg] = ImVec4(0.06f, 0.06f, 0.08f, 1.0f);
    colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.26f, 0.59f, 0.98f, 0.5f);
    colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.26f, 0.59f, 0.98f, 0.7f);
    colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.26f, 0.59f, 0.98f, 1.0f);
    colors[ImGuiCol_CheckMark] = ImVec4(0.26f, 0.59f, 0.98f, 1.0f);
    colors[ImGuiCol_SliderGrab] = ImVec4(0.26f, 0.59f, 0.98f, 0.7f);
    colors[ImGuiCol_SliderGrabActive] = ImVec4(0.26f, 0.59f, 0.98f, 1.0f);
    colors[ImGuiCol_Button] = ImVec4(0.20f, 0.25f, 0.30f, 1.0f);
    colors[ImGuiCol_ButtonHovered] = ImVec4(0.25f, 0.30f, 0.35f, 1.0f);
    colors[ImGuiCol_ButtonActive] = ImVec4(0.30f, 0.35f, 0.40f, 1.0f);
    colors[ImGuiCol_Header] = ImVec4(0.12f, 0.12f, 0.15f, 1.0f);
    colors[ImGuiCol_HeaderHovered] = ImVec4(0.15f, 0.15f, 0.18f, 1.0f);
    colors[ImGuiCol_HeaderActive] = ImVec4(0.18f, 0.18f, 0.22f, 1.0f);
    colors[ImGuiCol_Separator] = ImVec4(0.26f, 0.59f, 0.98f, 0.3f);
    colors[ImGuiCol_SeparatorHovered] = ImVec4(0.26f, 0.59f, 0.98f, 0.5f);
    colors[ImGuiCol_SeparatorActive] = ImVec4(0.26f, 0.59f, 0.98f, 1.0f);
    colors[ImGuiCol_ResizeGrip] = ImVec4(0.26f, 0.59f, 0.98f, 0.3f);
    colors[ImGuiCol_ResizeGripHovered] = ImVec4(0.26f, 0.59f, 0.98f, 0.5f);
    colors[ImGuiCol_ResizeGripActive] = ImVec4(0.26f, 0.59f, 0.98f, 1.0f);
    colors[ImGuiCol_Tab] = ImVec4(0.12f, 0.12f, 0.15f, 1.0f);
    colors[ImGuiCol_TabHovered] = ImVec4(0.26f, 0.59f, 0.98f, 0.8f);
    colors[ImGuiCol_TabActive] = ImVec4(0.26f, 0.59f, 0.98f, 1.0f);
    colors[ImGuiCol_TabUnfocused] = ImVec4(0.08f, 0.08f, 0.10f, 1.0f);
    colors[ImGuiCol_TabUnfocusedActive] = ImVec4(0.15f, 0.15f, 0.18f, 1.0f);
    colors[ImGuiCol_DockingPreview] = ImVec4(0.26f, 0.59f, 0.98f, 0.7f);
    colors[ImGuiCol_DockingEmptyBg] = ImVec4(0.06f, 0.06f, 0.08f, 1.0f);
    colors[ImGuiCol_PlotLines] = ImVec4(0.26f, 0.59f, 0.98f, 1.0f);
    colors[ImGuiCol_PlotLinesHovered] = ImVec4(1.00f, 0.43f, 0.35f, 1.0f);
    colors[ImGuiCol_PlotHistogram] = ImVec4(0.26f, 0.59f, 0.98f, 1.0f);
    colors[ImGuiCol_PlotHistogramHovered] = ImVec4(1.00f, 0.60f, 0.00f, 1.0f);
    colors[ImGuiCol_TableHeaderBg] = ImVec4(0.12f, 0.12f, 0.15f, 1.0f);
    colors[ImGuiCol_TableBorderStrong] = ImVec4(0.26f, 0.59f, 0.98f, 0.5f);
    colors[ImGuiCol_TableBorderLight] = ImVec4(0.26f, 0.59f, 0.98f, 0.2f);
    colors[ImGuiCol_TableRowBg] = ImVec4(0.00f, 0.00f, 0.00f, 0.0f);
    colors[ImGuiCol_TableRowBgAlt] = ImVec4(1.00f, 1.00f, 1.00f, 0.03f);
    colors[ImGuiCol_TextSelectedBg] = ImVec4(0.26f, 0.59f, 0.98f, 0.35f);
    colors[ImGuiCol_DragDropTarget] = ImVec4(1.00f, 1.00f, 0.00f, 0.90f);
    colors[ImGuiCol_NavHighlight] = ImVec4(0.26f, 0.59f, 0.98f, 1.0f);
    colors[ImGuiCol_NavWindowingHighlight] = ImVec4(1.00f, 1.00f, 1.00f, 0.70f);
    colors[ImGuiCol_NavWindowingDimBg] = ImVec4(0.80f, 0.80f, 0.80f, 0.20f);
    colors[ImGuiCol_ModalWindowDimBg] = ImVec4(0.80f, 0.80f, 0.80f, 0.35f);
}

void ProtectionUI::SetProfessionalTheme() {
    ImVec4* colors = ImGui::GetStyle().Colors;

    // Professional dark theme
    colors[ImGuiCol_Text] = ImVec4(0.90f, 0.90f, 0.90f, 1.0f);
    colors[ImGuiCol_TextDisabled] = ImVec4(0.60f, 0.60f, 0.60f, 1.0f);
    colors[ImGuiCol_WindowBg] = ImVec4(0.13f, 0.14f, 0.15f, 1.0f);
    colors[ImGuiCol_ChildBg] = ImVec4(0.15f, 0.16f, 0.17f, 1.0f);
    colors[ImGuiCol_PopupBg] = ImVec4(0.13f, 0.14f, 0.15f, 1.0f);
    colors[ImGuiCol_Border] = ImVec4(0.43f, 0.43f, 0.50f, 0.5f);
    colors[ImGuiCol_BorderShadow] = ImVec4(0.00f, 0.00f, 0.00f, 0.0f);
    colors[ImGuiCol_FrameBg] = ImVec4(0.20f, 0.21f, 0.22f, 1.0f);
    colors[ImGuiCol_FrameBgHovered] = ImVec4(0.40f, 0.40f, 0.40f, 0.4f);
    colors[ImGuiCol_FrameBgActive] = ImVec4(0.18f, 0.18f, 0.18f, 0.67f);
    colors[ImGuiCol_TitleBg] = ImVec4(0.04f, 0.04f, 0.04f, 1.0f);
    colors[ImGuiCol_TitleBgActive] = ImVec4(0.29f, 0.29f, 0.29f, 1.0f);
    colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.00f, 0.00f, 0.00f, 0.51f);
    colors[ImGuiCol_MenuBarBg] = ImVec4(0.14f, 0.14f, 0.14f, 1.0f);
    colors[ImGuiCol_ScrollbarBg] = ImVec4(0.02f, 0.02f, 0.02f, 0.53f);
    colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.31f, 0.31f, 0.31f, 1.0f);
    colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.41f, 0.41f, 0.41f, 1.0f);
    colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.51f, 0.51f, 0.51f, 1.0f);
    colors[ImGuiCol_CheckMark] = ImVec4(0.94f, 0.94f, 0.94f, 1.0f);
    colors[ImGuiCol_SliderGrab] = ImVec4(0.51f, 0.51f, 0.51f, 1.0f);
    colors[ImGuiCol_SliderGrabActive] = ImVec4(0.86f, 0.86f, 0.86f, 1.0f);
    colors[ImGuiCol_Button] = ImVec4(0.44f, 0.44f, 0.44f, 0.4f);
    colors[ImGuiCol_ButtonHovered] = ImVec4(0.46f, 0.47f, 0.48f, 1.0f);
    colors[ImGuiCol_ButtonActive] = ImVec4(0.42f, 0.42f, 0.42f, 1.0f);
    colors[ImGuiCol_Header] = ImVec4(0.70f, 0.70f, 0.70f, 0.31f);
    colors[ImGuiCol_HeaderHovered] = ImVec4(0.70f, 0.70f, 0.70f, 0.80f);
    colors[ImGuiCol_HeaderActive] = ImVec4(0.48f, 0.50f, 0.52f, 1.0f);
    colors[ImGuiCol_Separator] = ImVec4(0.43f, 0.43f, 0.50f, 0.5f);
    colors[ImGuiCol_SeparatorHovered] = ImVec4(0.72f, 0.72f, 0.72f, 0.78f);
    colors[ImGuiCol_SeparatorActive] = ImVec4(0.51f, 0.51f, 0.51f, 1.0f);
    colors[ImGuiCol_ResizeGrip] = ImVec4(0.91f, 0.91f, 0.91f, 0.25f);
    colors[ImGuiCol_ResizeGripHovered] = ImVec4(0.81f, 0.81f, 0.81f, 0.67f);
    colors[ImGuiCol_ResizeGripActive] = ImVec4(0.46f, 0.46f, 0.46f, 0.95f);
    colors[ImGuiCol_PlotLines] = ImVec4(0.61f, 0.61f, 0.61f, 1.0f);
    colors[ImGuiCol_PlotLinesHovered] = ImVec4(1.00f, 0.43f, 0.35f, 1.0f);
    colors[ImGuiCol_PlotHistogram] = ImVec4(0.73f, 0.60f, 0.15f, 1.0f);
    colors[ImGuiCol_PlotHistogramHovered] = ImVec4(1.00f, 0.60f, 0.00f, 1.0f);
    colors[ImGuiCol_TextSelectedBg] = ImVec4(0.87f, 0.87f, 0.87f, 0.35f);
    colors[ImGuiCol_ModalWindowDimBg] = ImVec4(0.80f, 0.80f, 0.80f, 0.35f);
    colors[ImGuiCol_DragDropTarget] = ImVec4(1.00f, 1.00f, 0.00f, 0.90f);
    colors[ImGuiCol_NavHighlight] = ImVec4(0.60f, 0.60f, 0.60f, 1.0f);
    colors[ImGuiCol_NavWindowingHighlight] = ImVec4(1.00f, 1.00f, 1.00f, 0.70f);
}

void ProtectionUI::SetSecurityTheme() {
    ImVec4* colors = ImGui::GetStyle().Colors;

    // Security blue theme
    colors[ImGuiCol_Text] = ImVec4(0.95f, 0.95f, 0.95f, 1.0f);
    colors[ImGuiCol_TextDisabled] = ImVec4(0.50f, 0.50f, 0.50f, 1.0f);
    colors[ImGuiCol_WindowBg] = ImVec4(0.06f, 0.08f, 0.12f, 1.0f);
    colors[ImGuiCol_ChildBg] = ImVec4(0.08f, 0.10f, 0.14f, 1.0f);
    colors[ImGuiCol_PopupBg] = ImVec4(0.06f, 0.08f, 0.12f, 1.0f);
    colors[ImGuiCol_Border] = ImVec4(0.20f, 0.40f, 0.80f, 0.5f);
    colors[ImGuiCol_BorderShadow] = ImVec4(0.00f, 0.00f, 0.00f, 0.0f);
    colors[ImGuiCol_FrameBg] = ImVec4(0.10f, 0.12f, 0.16f, 1.0f);
    colors[ImGuiCol_FrameBgHovered] = ImVec4(0.12f, 0.14f, 0.18f, 1.0f);
    colors[ImGuiCol_FrameBgActive] = ImVec4(0.14f, 0.16f, 0.20f, 1.0f);
    colors[ImGuiCol_TitleBg] = ImVec4(0.04f, 0.06f, 0.10f, 1.0f);
    colors[ImGuiCol_TitleBgActive] = ImVec4(0.20f, 0.40f, 0.80f, 1.0f);
    colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.04f, 0.06f, 0.10f, 0.75f);
    colors[ImGuiCol_MenuBarBg] = ImVec4(0.08f, 0.10f, 0.14f, 1.0f);
    colors[ImGuiCol_ScrollbarBg] = ImVec4(0.04f, 0.06f, 0.10f, 1.0f);
    colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.20f, 0.40f, 0.80f, 0.5f);
    colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.20f, 0.40f, 0.80f, 0.7f);
    colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.20f, 0.40f, 0.80f, 1.0f);
    colors[ImGuiCol_CheckMark] = ImVec4(0.20f, 0.40f, 0.80f, 1.0f);
    colors[ImGuiCol_SliderGrab] = ImVec4(0.20f, 0.40f, 0.80f, 0.7f);
    colors[ImGuiCol_SliderGrabActive] = ImVec4(0.20f, 0.40f, 0.80f, 1.0f);
    colors[ImGuiCol_Button] = ImVec4(0.15f, 0.20f, 0.30f, 1.0f);
    colors[ImGuiCol_ButtonHovered] = ImVec4(0.20f, 0.25f, 0.35f, 1.0f);
    colors[ImGuiCol_ButtonActive] = ImVec4(0.25f, 0.30f, 0.40f, 1.0f);
    colors[ImGuiCol_Header] = ImVec4(0.10f, 0.12f, 0.16f, 1.0f);
    colors[ImGuiCol_HeaderHovered] = ImVec4(0.12f, 0.14f, 0.18f, 1.0f);
    colors[ImGuiCol_HeaderActive] = ImVec4(0.14f, 0.16f, 0.20f, 1.0f);
    colors[ImGuiCol_Separator] = ImVec4(0.20f, 0.40f, 0.80f, 0.3f);
    colors[ImGuiCol_SeparatorHovered] = ImVec4(0.20f, 0.40f, 0.80f, 0.5f);
    colors[ImGuiCol_SeparatorActive] = ImVec4(0.20f, 0.40f, 0.80f, 1.0f);
    colors[ImGuiCol_ResizeGrip] = ImVec4(0.20f, 0.40f, 0.80f, 0.3f);
    colors[ImGuiCol_ResizeGripHovered] = ImVec4(0.20f, 0.40f, 0.80f, 0.5f);
    colors[ImGuiCol_ResizeGripActive] = ImVec4(0.20f, 0.40f, 0.80f, 1.0f);
    colors[ImGuiCol_PlotLines] = ImVec4(0.20f, 0.40f, 0.80f, 1.0f);
    colors[ImGuiCol_PlotLinesHovered] = ImVec4(1.00f, 0.43f, 0.35f, 1.0f);
    colors[ImGuiCol_PlotHistogram] = ImVec4(0.20f, 0.40f, 0.80f, 1.0f);
    colors[ImGuiCol_PlotHistogramHovered] = ImVec4(1.00f, 0.60f, 0.00f, 1.0f);
    colors[ImGuiCol_TextSelectedBg] = ImVec4(0.20f, 0.40f, 0.80f, 0.35f);
    colors[ImGuiCol_ModalWindowDimBg] = ImVec4(0.80f, 0.80f, 0.80f, 0.35f);
    colors[ImGuiCol_DragDropTarget] = ImVec4(1.00f, 1.00f, 0.00f, 0.90f);
    colors[ImGuiCol_NavHighlight] = ImVec4(0.20f, 0.40f, 0.80f, 1.0f);
    colors[ImGuiCol_NavWindowingHighlight] = ImVec4(1.00f, 1.00f, 1.00f, 0.70f);
}

void ProtectionUI::LoadFonts() {
    ImGuiIO& io = ImGui::GetIO();

    // Load default font with custom size
    io.Fonts->AddFontDefault();

    // Try to load custom fonts if available
    if (m_config.customFontPath.empty()) {
        // Load system fonts
        m_fonts.regular = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\segoeui.ttf", m_config.fontSize);
        m_fonts.bold = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\segoeuib.ttf", m_config.fontSize);
        m_fonts.monospace = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\consola.ttf", m_config.fontSize);
        m_fonts.large = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\segoeui.ttf", m_config.fontSize * 1.5f);
    } else {
        // Load custom font
        m_fonts.regular = io.Fonts->AddFontFromFileTTF(m_config.customFontPath.c_str(), m_config.fontSize);
    }

    // Fallback to default if loading failed
    if (!m_fonts.regular) m_fonts.regular = io.Fonts->Fonts[0];
    if (!m_fonts.bold) m_fonts.bold = m_fonts.regular;
    if (!m_fonts.monospace) m_fonts.monospace = m_fonts.regular;
    if (!m_fonts.large) m_fonts.large = m_fonts.regular;

    // Build font atlas
    io.Fonts->Build();
}

// DirectX 11 helper functions
bool CreateDeviceD3D(HWND hWnd) {
    // Setup swap chain
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = hWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    UINT createDeviceFlags = 0;
    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0, };

    HRESULT res = D3D11CreateDeviceAndSwapChain(
        nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, createDeviceFlags,
        featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain,
        &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext);

    if (res == DXGI_ERROR_UNSUPPORTED) {
        res = D3D11CreateDeviceAndSwapChain(
            nullptr, D3D_DRIVER_TYPE_WARP, nullptr, createDeviceFlags,
            featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain,
            &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext);
    }

    if (res != S_OK) {
        return false;
    }

    CreateRenderTarget();
    return true;
}

void CleanupDeviceD3D() {
    CleanupRenderTarget();
    if (g_pSwapChain) { g_pSwapChain->Release(); g_pSwapChain = nullptr; }
    if (g_pd3dDeviceContext) { g_pd3dDeviceContext->Release(); g_pd3dDeviceContext = nullptr; }
    if (g_pd3dDevice) { g_pd3dDevice->Release(); g_pd3dDevice = nullptr; }
}

void CreateRenderTarget() {
    ID3D11Texture2D* pBackBuffer;
    g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    g_pd3dDevice->CreateRenderTargetView(pBackBuffer, nullptr, &g_mainRenderTargetView);
    pBackBuffer->Release();
}

void CleanupRenderTarget() {
    if (g_mainRenderTargetView) {
        g_mainRenderTargetView->Release();
        g_mainRenderTargetView = nullptr;
    }
}

// Theme implementation functions
void ProtectionUI::ApplyCyberTheme() {
    ImVec4* colors = ImGui::GetStyle().Colors;
    colors[ImGuiCol_Text] = ImVec4(0.95f, 0.95f, 0.95f, 1.0f);
    colors[ImGuiCol_WindowBg] = ImVec4(0.08f, 0.08f, 0.10f, 1.0f);
    colors[ImGuiCol_TitleBgActive] = ImVec4(0.26f, 0.59f, 0.98f, 1.0f);
    colors[ImGuiCol_Button] = ImVec4(0.26f, 0.59f, 0.98f, 0.40f);
    colors[ImGuiCol_ButtonHovered] = ImVec4(0.26f, 0.59f, 0.98f, 1.0f);
    colors[ImGuiCol_ButtonActive] = ImVec4(0.06f, 0.53f, 0.98f, 1.0f);
}

void ProtectionUI::ApplyProfessionalTheme() {
    ImVec4* colors = ImGui::GetStyle().Colors;
    colors[ImGuiCol_Text] = ImVec4(0.90f, 0.90f, 0.90f, 1.0f);
    colors[ImGuiCol_WindowBg] = ImVec4(0.13f, 0.14f, 0.15f, 1.0f);
    colors[ImGuiCol_TitleBgActive] = ImVec4(0.16f, 0.29f, 0.48f, 1.0f);
    colors[ImGuiCol_Button] = ImVec4(0.20f, 0.25f, 0.29f, 1.0f);
    colors[ImGuiCol_ButtonHovered] = ImVec4(0.28f, 0.56f, 1.0f, 1.0f);
    colors[ImGuiCol_ButtonActive] = ImVec4(0.06f, 0.53f, 0.98f, 1.0f);
}

void ProtectionUI::ApplySecurityTheme() {
    ImVec4* colors = ImGui::GetStyle().Colors;
    colors[ImGuiCol_Text] = ImVec4(0.85f, 0.85f, 0.85f, 1.0f);
    colors[ImGuiCol_WindowBg] = ImVec4(0.05f, 0.10f, 0.15f, 1.0f);
    colors[ImGuiCol_TitleBgActive] = ImVec4(0.0f, 0.4f, 0.8f, 1.0f);
    colors[ImGuiCol_Button] = ImVec4(0.0f, 0.4f, 0.8f, 0.6f);
    colors[ImGuiCol_ButtonHovered] = ImVec4(0.0f, 0.5f, 1.0f, 1.0f);
    colors[ImGuiCol_ButtonActive] = ImVec4(0.0f, 0.3f, 0.7f, 1.0f);
}
