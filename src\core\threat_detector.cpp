#include "threat_detector.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <sstream>
#include <array>
#include <psapi.h>
#include <tlhelp32.h>
#include <winternl.h>
#include <cmath>

// Define missing PEB structure members
typedef struct _PEB_CUSTOM {
    BYTE Reserved1[2];
    BYTE BeingDebugged;
    BYTE Reserved2[1];
    PVOID Reserved3[2];
    PPEB_LDR_DATA Ldr;
    PRTL_USER_PROCESS_PARAMETERS ProcessParameters;
    PVOID Reserved4[3];
    PVOID AtlThunkSListPtr;
    PVOID Reserved5;
    ULONG SessionId;
    ULARGE_INTEGER Reserved6;
    PVOID Reserved7;
    ULONG NtGlobalFlag;  // This is what we need
    // ... other members
} PEB_CUSTOM, *PPEB_CUSTOM;

ThreatDetector::ThreatDetector() 
    : m_initialized(false), m_scanTimeout(30000), m_maxScanSize(100 * 1024 * 1024),
      m_minConfidence(ConfidenceLevel::MEDIUM) {
    
    // Enable all detection techniques by default
    m_enabledTechniques.insert(DetectionTechnique::SIGNATURE_BASED);
    m_enabledTechniques.insert(DetectionTechnique::HEURISTIC_ANALYSIS);
    m_enabledTechniques.insert(DetectionTechnique::BEHAVIOR_ANALYSIS);
    m_enabledTechniques.insert(DetectionTechnique::ANOMALY_DETECTION);
    
    memset(&m_statistics, 0, sizeof(m_statistics));
}

ThreatDetector::~ThreatDetector() {
    Shutdown();
}

bool ThreatDetector::Initialize() {
    if (m_initialized) {
        return true;
    }

    try {
        std::cout << "[ThreatDetector] Initializing threat detection system..." << std::endl;

        // Load default signatures
        LoadDefaultSignatures();
        
        // Load default detection rules
        LoadDefaultDetectionRules();
        
        // Load default behavioral patterns
        LoadDefaultBehavioralPatterns();

        m_initialized = true;
        std::cout << "[ThreatDetector] Successfully initialized with " 
                  << m_signatures.size() << " signatures, "
                  << m_detectionRules.size() << " rules, and "
                  << m_behavioralPatterns.size() << " patterns" << std::endl;
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[ThreatDetector] Initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void ThreatDetector::Shutdown() {
    if (!m_initialized) {
        return;
    }

    std::cout << "[ThreatDetector] Shutting down..." << std::endl;

    // Stop real-time detection
    if (m_realTimeActive) {
        StopRealTimeDetection();
    }

    // Wait for monitoring threads to finish
    m_shouldStop = true;
    for (HANDLE thread : m_monitoringThreads) {
        if (thread) {
            WaitForSingleObject(thread, 5000);
            CloseHandle(thread);
        }
    }
    m_monitoringThreads.clear();

    m_initialized = false;
    std::cout << "[ThreatDetector] Shutdown complete" << std::endl;
}

void ThreatDetector::LoadDefaultSignatures() {
    std::lock_guard<std::mutex> lock(m_signaturesMutex);

    // Anti-debugging signatures
    {
        ThreatSignature sig;
        sig.name = "IsDebuggerPresent_Call";
        sig.type = ThreatType::DEBUGGER_DETECTION;
        sig.severity = ThreatSeverity::HIGH;
        sig.description = "Call to IsDebuggerPresent API";
        sig.technique = DetectionTechnique::SIGNATURE_BASED;
        sig.confidence = ConfidenceLevel::HIGH;
        // Pattern for call to IsDebuggerPresent (simplified)
        sig.pattern = {0xFF, 0x15}; // call [address]
        sig.mask = {0xFF, 0xFF};
        m_signatures.push_back(sig);
        m_signatureNames.insert(sig.name);
    }

    // Process hollowing signatures
    {
        ThreatSignature sig;
        sig.name = "NtUnmapViewOfSection_Call";
        sig.type = ThreatType::PROCESS_HOLLOWING;
        sig.severity = ThreatSeverity::CRITICAL;
        sig.description = "Call to NtUnmapViewOfSection - Process Hollowing indicator";
        sig.technique = DetectionTechnique::SIGNATURE_BASED;
        sig.confidence = ConfidenceLevel::HIGH;
        // Pattern for syscall (simplified)
        sig.pattern = {0x0F, 0x05}; // syscall
        sig.mask = {0xFF, 0xFF};
        m_signatures.push_back(sig);
        m_signatureNames.insert(sig.name);
    }

    // Code injection signatures
    {
        ThreatSignature sig;
        sig.name = "CreateRemoteThread_Call";
        sig.type = ThreatType::CODE_INJECTION;
        sig.severity = ThreatSeverity::CRITICAL;
        sig.description = "Call to CreateRemoteThread - DLL injection indicator";
        sig.technique = DetectionTechnique::SIGNATURE_BASED;
        sig.confidence = ConfidenceLevel::HIGH;
        sig.pattern = {0xFF, 0x15}; // call [address]
        sig.mask = {0xFF, 0xFF};
        m_signatures.push_back(sig);
        m_signatureNames.insert(sig.name);
    }

    // Shellcode patterns
    {
        ThreatSignature sig;
        sig.name = "Common_Shellcode_Pattern";
        sig.type = ThreatType::CODE_INJECTION;
        sig.severity = ThreatSeverity::HIGH;
        sig.description = "Common shellcode pattern detected";
        sig.technique = DetectionTechnique::SIGNATURE_BASED;
        sig.confidence = ConfidenceLevel::MEDIUM;
        // Common shellcode prologue
        sig.pattern = {0xFC, 0x48, 0x83, 0xE4, 0xF0}; // cld; and rsp, -16
        sig.mask = {0xFF, 0xFF, 0xFF, 0xFF, 0xFF};
        m_signatures.push_back(sig);
        m_signatureNames.insert(sig.name);
    }

    std::cout << "[ThreatDetector] Loaded " << m_signatures.size() << " default signatures" << std::endl;
}

void ThreatDetector::LoadDefaultDetectionRules() {
    std::lock_guard<std::mutex> lock(m_rulesMutex);

    // Suspicious process name rule
    {
        DetectionRule rule;
        rule.name = "Suspicious_Process_Names";
        rule.description = "Detects processes with suspicious names";
        rule.threatType = ThreatType::UNKNOWN;
        rule.severity = ThreatSeverity::MEDIUM;
        rule.technique = DetectionTechnique::HEURISTIC_ANALYSIS;
        rule.confidence = ConfidenceLevel::MEDIUM;
        rule.condition = [](const ProcessInfo& process) -> bool {
            std::string name = process.processName;
            std::transform(name.begin(), name.end(), name.begin(), ::tolower);
            
            // Check for suspicious names
            std::vector<std::string> suspiciousNames = {
                "svchost.exe", "winlogon.exe", "explorer.exe", "lsass.exe"
            };
            
            for (const auto& suspicious : suspiciousNames) {
                if (name.find(suspicious) != std::string::npos) {
                    // Check if it's in the correct location
                    if (process.imagePath.find("\\System32\\") == std::string::npos &&
                        process.imagePath.find("\\SysWOW64\\") == std::string::npos) {
                        return true; // Suspicious location
                    }
                }
            }
            return false;
        };
        m_detectionRules.push_back(rule);
    }

    // High memory usage rule
    {
        DetectionRule rule;
        rule.name = "High_Memory_Usage";
        rule.description = "Detects processes with unusually high memory usage";
        rule.threatType = ThreatType::UNKNOWN;
        rule.severity = ThreatSeverity::LOW;
        rule.technique = DetectionTechnique::ANOMALY_DETECTION;
        rule.confidence = ConfidenceLevel::LOW;
        rule.condition = [](const ProcessInfo& process) -> bool {
            return process.imageSize > (500 * 1024 * 1024); // > 500MB
        };
        m_detectionRules.push_back(rule);
    }

    std::cout << "[ThreatDetector] Loaded " << m_detectionRules.size() << " default detection rules" << std::endl;
}

void ThreatDetector::LoadDefaultBehavioralPatterns() {
    std::lock_guard<std::mutex> lock(m_patternsMutex);

    // Process injection pattern
    {
        BehavioralPattern pattern;
        pattern.name = "Process_Injection_Pattern";
        pattern.apiSequence = {
            "OpenProcess",
            "VirtualAllocEx", 
            "WriteProcessMemory",
            "CreateRemoteThread"
        };
        pattern.associatedThreat = ThreatType::CODE_INJECTION;
        pattern.suspicionWeight = 0.8;
        pattern.timeWindowMs = 10000; // 10 seconds
        pattern.requiresSequentialOrder = true;
        m_behavioralPatterns.push_back(pattern);
    }

    // Process hollowing pattern
    {
        BehavioralPattern pattern;
        pattern.name = "Process_Hollowing_Pattern";
        pattern.apiSequence = {
            "CreateProcess",
            "NtUnmapViewOfSection",
            "VirtualAllocEx",
            "WriteProcessMemory",
            "SetThreadContext",
            "ResumeThread"
        };
        pattern.associatedThreat = ThreatType::PROCESS_HOLLOWING;
        pattern.suspicionWeight = 0.9;
        pattern.timeWindowMs = 15000; // 15 seconds
        pattern.requiresSequentialOrder = true;
        m_behavioralPatterns.push_back(pattern);
    }

    // Keylogger pattern
    {
        BehavioralPattern pattern;
        pattern.name = "Keylogger_Pattern";
        pattern.apiSequence = {
            "SetWindowsHookEx",
            "GetAsyncKeyState",
            "GetKeyboardState"
        };
        pattern.associatedThreat = ThreatType::KEYLOGGER;
        pattern.suspicionWeight = 0.7;
        pattern.timeWindowMs = 30000; // 30 seconds
        pattern.requiresSequentialOrder = false;
        m_behavioralPatterns.push_back(pattern);
    }

    std::cout << "[ThreatDetector] Loaded " << m_behavioralPatterns.size() << " default behavioral patterns" << std::endl;
}

std::vector<MemoryScanResult> ThreatDetector::ScanMemory(uintptr_t address, size_t size) {
    std::vector<MemoryScanResult> results;
    
    if (!m_initialized || size == 0 || size > m_maxScanSize) {
        return results;
    }

    try {
        // Read memory
        std::vector<uint8_t> data(size);
        SIZE_T bytesRead;
        if (!::ReadProcessMemory(GetCurrentProcess(),
                              reinterpret_cast<LPCVOID>(address),
                              data.data(), size, &bytesRead)) {
            return results;
        }

        data.resize(bytesRead);

        // Scan with signatures
        if (m_enabledTechniques.count(DetectionTechnique::SIGNATURE_BASED)) {
            ScanWithSignatures(data, address, results);
        }

        // Heuristic analysis
        if (m_enabledTechniques.count(DetectionTechnique::HEURISTIC_ANALYSIS)) {
            PerformHeuristicAnalysis(data, address, results);
        }

        UpdateStatistics();
        
    }
    catch (const std::exception& e) {
        std::cerr << "[ThreatDetector] Exception in ScanMemory: " << e.what() << std::endl;
    }

    return results;
}

void ThreatDetector::ScanWithSignatures(const std::vector<uint8_t>& data, uintptr_t baseAddress, 
                                       std::vector<MemoryScanResult>& results) {
    std::lock_guard<std::mutex> lock(m_signaturesMutex);

    for (const auto& signature : m_signatures) {
        if (!signature.isEnabled || signature.confidence < m_minConfidence) {
            continue;
        }

        // Search for pattern in data
        for (size_t i = 0; i <= data.size() - signature.pattern.size(); ++i) {
            bool match = true;
            for (size_t j = 0; j < signature.pattern.size(); ++j) {
                if ((data[i + j] & signature.mask[j]) != (signature.pattern[j] & signature.mask[j])) {
                    match = false;
                    break;
                }
            }

            if (match) {
                MemoryScanResult result;
                result.address = baseAddress + i;
                result.size = signature.pattern.size();
                result.matchedSignatures.push_back(signature);
                result.detectedThreat = signature.type;
                result.severity = signature.severity;
                result.confidence = signature.confidence;
                result.description = signature.description;
                GetSystemTime(&result.scanTime);
                
                // Copy suspicious data
                size_t copySize = std::min(static_cast<size_t>(64), data.size() - i);
                result.suspiciousData.assign(data.begin() + i, data.begin() + i + copySize);
                
                results.push_back(result);
                
                std::cout << "[ThreatDetector] Signature match: " << signature.name 
                          << " at 0x" << std::hex << result.address << std::endl;
            }
        }
    }
}

void ThreatDetector::PerformHeuristicAnalysis(const std::vector<uint8_t>& data, uintptr_t baseAddress,
                                            std::vector<MemoryScanResult>& results) {
    // Entropy analysis
    if (AnalyzeEntropy(data)) {
        MemoryScanResult result;
        result.address = baseAddress;
        result.size = data.size();
        result.detectedThreat = ThreatType::UNKNOWN;
        result.severity = ThreatSeverity::MEDIUM;
        result.confidence = ConfidenceLevel::MEDIUM;
        result.description = "High entropy detected - possible packed/encrypted code";
        GetSystemTime(&result.scanTime);
        results.push_back(result);
    }

    // String pattern analysis
    if (AnalyzeStringPatterns(data)) {
        MemoryScanResult result;
        result.address = baseAddress;
        result.size = data.size();
        result.detectedThreat = ThreatType::UNKNOWN;
        result.severity = ThreatSeverity::LOW;
        result.confidence = ConfidenceLevel::LOW;
        result.description = "Suspicious string patterns detected";
        GetSystemTime(&result.scanTime);
        results.push_back(result);
    }
}

bool ThreatDetector::AnalyzeEntropy(const std::vector<uint8_t>& data) {
    if (data.size() < 256) {
        return false;
    }

    // Calculate Shannon entropy
    std::array<int, 256> frequency = {};
    for (uint8_t byte : data) {
        frequency[byte]++;
    }

    double entropy = 0.0;
    double dataSize = static_cast<double>(data.size());
    
    for (int freq : frequency) {
        if (freq > 0) {
            double probability = freq / dataSize;
            entropy -= probability * log2(probability);
        }
    }

    // High entropy threshold (7.0 out of 8.0 max)
    return entropy > 7.0;
}

bool ThreatDetector::AnalyzeStringPatterns(const std::vector<uint8_t>& data) {
    std::string dataStr(data.begin(), data.end());
    std::transform(dataStr.begin(), dataStr.end(), dataStr.begin(), ::tolower);

    // Suspicious strings
    std::vector<std::string> suspiciousStrings = {
        "kernel32.dll", "ntdll.dll", "loadlibrary", "getprocaddress",
        "virtualalloc", "createremotethread", "writeprocessmemory",
        "isdebuggerpresent", "checkremotedebugger", "ntqueryinformationprocess"
    };

    int matches = 0;
    for (const auto& suspicious : suspiciousStrings) {
        if (dataStr.find(suspicious) != std::string::npos) {
            matches++;
        }
    }

    // Threshold: 3 or more suspicious strings
    return matches >= 3;
}

double ThreatDetector::CalculateSuspicionScore(DWORD processId) {
    double score = 0.0;

    try {
        // Get process handle
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
        if (!hProcess) {
            return 0.0;
        }

        // Check process image path
        char imagePath[MAX_PATH];
        if (GetModuleFileNameExA(hProcess, nullptr, imagePath, MAX_PATH)) {
            std::string path(imagePath);
            std::transform(path.begin(), path.end(), path.begin(), ::tolower);

            // Suspicious locations
            if (path.find("\\temp\\") != std::string::npos ||
                path.find("\\appdata\\") != std::string::npos ||
                path.find("\\downloads\\") != std::string::npos) {
                score += 0.3;
            }

            // System directory impersonation
            if (path.find("\\system32\\") == std::string::npos &&
                path.find("\\syswow64\\") == std::string::npos) {
                std::string filename = path.substr(path.find_last_of("\\") + 1);
                std::vector<std::string> systemFiles = {
                    "svchost.exe", "winlogon.exe", "explorer.exe", "lsass.exe"
                };

                for (const auto& sysFile : systemFiles) {
                    if (filename == sysFile) {
                        score += 0.5; // High suspicion for system file in wrong location
                        break;
                    }
                }
            }
        }

        // Check memory usage
        PROCESS_MEMORY_COUNTERS pmc;
        if (GetProcessMemoryInfo(hProcess, &pmc, sizeof(pmc))) {
            // Unusually high memory usage
            if (pmc.WorkingSetSize > 500 * 1024 * 1024) { // > 500MB
                score += 0.2;
            }
        }

        CloseHandle(hProcess);

    }
    catch (const std::exception& e) {
        std::cerr << "[ThreatDetector] Exception in CalculateSuspicionScore: " << e.what() << std::endl;
    }

    return std::min(score, 1.0); // Cap at 1.0
}

bool ThreatDetector::DetectDebugger() {
    bool debuggerDetected = false;

    try {
        // Check IsDebuggerPresent
        if (IsDebuggerPresent()) {
            debuggerDetected = true;
        }

        // Check remote debugger
        BOOL remoteDebugger = FALSE;
        if (CheckRemoteDebuggerPresent(GetCurrentProcess(), &remoteDebugger) && remoteDebugger) {
            debuggerDetected = true;
        }

        // Check NtGlobalFlag
        PPEB_CUSTOM peb = reinterpret_cast<PPEB_CUSTOM>(__readgsqword(0x60));
        if (peb && (peb->NtGlobalFlag & 0x70)) {
            debuggerDetected = true;
        }

        // Check heap flags
        PVOID heap = GetProcessHeap();
        if (heap) {
            DWORD* heapFlags = reinterpret_cast<DWORD*>(static_cast<char*>(heap) + 0x40);
            if (*heapFlags & 0x50000062) {
                debuggerDetected = true;
            }
        }

        if (debuggerDetected && m_threatCallback) {
            ThreatInfo threat;
            threat.type = ThreatType::DEBUGGER_DETECTION;
            threat.severity = ThreatSeverity::HIGH;
            threat.description = "Debugger presence detected";
            threat.processId = GetCurrentProcessId();
            threat.threadId = GetCurrentThreadId();
            GetSystemTime(&threat.timestamp);
            m_threatCallback(threat);
        }

    }
    catch (const std::exception& e) {
        std::cerr << "[ThreatDetector] Exception in DetectDebugger: " << e.what() << std::endl;
    }

    return debuggerDetected;
}

bool ThreatDetector::DetectProcessHollowing(DWORD processId) {
    try {
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
        if (!hProcess) {
            return false;
        }

        // Get process image base
        HMODULE hMod;
        DWORD cbNeeded;
        if (!EnumProcessModules(hProcess, &hMod, sizeof(hMod), &cbNeeded)) {
            CloseHandle(hProcess);
            return false;
        }

        // Read PE header
        IMAGE_DOS_HEADER dosHeader;
        SIZE_T bytesRead;
        if (!::ReadProcessMemory(hProcess, hMod, &dosHeader, sizeof(dosHeader), &bytesRead) ||
            dosHeader.e_magic != IMAGE_DOS_SIGNATURE) {
            CloseHandle(hProcess);
            return false;
        }

        IMAGE_NT_HEADERS ntHeaders;
        if (!::ReadProcessMemory(hProcess,
                              reinterpret_cast<char*>(hMod) + dosHeader.e_lfanew,
                              &ntHeaders, sizeof(ntHeaders), &bytesRead) ||
            ntHeaders.Signature != IMAGE_NT_SIGNATURE) {
            CloseHandle(hProcess);
            return false;
        }

        // Check if entry point is within the image
        uintptr_t entryPoint = reinterpret_cast<uintptr_t>(hMod) + ntHeaders.OptionalHeader.AddressOfEntryPoint;
        uintptr_t imageBase = reinterpret_cast<uintptr_t>(hMod);
        uintptr_t imageEnd = imageBase + ntHeaders.OptionalHeader.SizeOfImage;

        bool hollowingDetected = false;
        if (entryPoint < imageBase || entryPoint >= imageEnd) {
            hollowingDetected = true;
        }

        // Check for suspicious memory regions
        MEMORY_BASIC_INFORMATION mbi;
        uintptr_t address = imageBase;
        while (address < imageEnd) {
            if (VirtualQueryEx(hProcess, reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi))) {
                // Check for executable regions that shouldn't be there
                if ((mbi.Protect & PAGE_EXECUTE_READWRITE) &&
                    (mbi.Type == MEM_PRIVATE)) {
                    hollowingDetected = true;
                    break;
                }
                address += mbi.RegionSize;
            } else {
                break;
            }
        }

        CloseHandle(hProcess);

        if (hollowingDetected && m_threatCallback) {
            ThreatInfo threat;
            threat.type = ThreatType::PROCESS_HOLLOWING;
            threat.severity = ThreatSeverity::CRITICAL;
            threat.description = "Process hollowing detected";
            threat.processId = processId;
            threat.address = entryPoint;
            GetSystemTime(&threat.timestamp);
            m_threatCallback(threat);
        }

        return hollowingDetected;

    }
    catch (const std::exception& e) {
        std::cerr << "[ThreatDetector] Exception in DetectProcessHollowing: " << e.what() << std::endl;
        return false;
    }
}

bool ThreatDetector::StartRealTimeDetection() {
    if (m_realTimeActive) {
        return true;
    }

    try {
        std::cout << "[ThreatDetector] Starting real-time detection..." << std::endl;

        m_shouldStop = false;

        // Start monitoring thread
        HANDLE monitoringThread = CreateThread(
            nullptr, 0, RealTimeMonitoringThread, this, 0, nullptr
        );
        if (monitoringThread) {
            m_monitoringThreads.push_back(monitoringThread);
        }

        // Start behavior analysis thread
        HANDLE behaviorThread = CreateThread(
            nullptr, 0, BehaviorAnalysisThread, this, 0, nullptr
        );
        if (behaviorThread) {
            m_monitoringThreads.push_back(behaviorThread);
        }

        m_realTimeActive = true;
        std::cout << "[ThreatDetector] Real-time detection started" << std::endl;

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[ThreatDetector] Failed to start real-time detection: " << e.what() << std::endl;
        return false;
    }
}

bool ThreatDetector::StopRealTimeDetection() {
    if (!m_realTimeActive) {
        return true;
    }

    std::cout << "[ThreatDetector] Stopping real-time detection..." << std::endl;

    m_shouldStop = true;
    m_realTimeActive = false;

    std::cout << "[ThreatDetector] Real-time detection stopped" << std::endl;
    return true;
}

DWORD WINAPI ThreatDetector::RealTimeMonitoringThread(LPVOID param) {
    ThreatDetector* detector = static_cast<ThreatDetector*>(param);

    std::cout << "[ThreatDetector] Real-time monitoring thread started" << std::endl;

    while (!detector->m_shouldStop) {
        try {
            // Periodic debugger detection
            detector->DetectDebugger();

            // Scan current process
            std::vector<MemoryScanResult> results = detector->ScanProcess(GetCurrentProcessId());
            for (const auto& result : results) {
                if (detector->m_threatCallback) {
                    ThreatInfo threat;
                    threat.type = result.detectedThreat;
                    threat.severity = result.severity;
                    threat.description = result.description;
                    threat.processId = GetCurrentProcessId();
                    threat.address = result.address;
                    threat.data = result.suspiciousData;
                    threat.timestamp = result.scanTime;
                    detector->m_threatCallback(threat);
                }
            }

            // Sleep for monitoring interval
            Sleep(5000); // 5 seconds
        }
        catch (const std::exception& e) {
            std::cerr << "[ThreatDetector] Exception in monitoring thread: " << e.what() << std::endl;
        }
    }

    std::cout << "[ThreatDetector] Real-time monitoring thread stopped" << std::endl;
    return 0;
}

DWORD WINAPI ThreatDetector::BehaviorAnalysisThread(LPVOID param) {
    ThreatDetector* detector = static_cast<ThreatDetector*>(param);

    std::cout << "[ThreatDetector] Behavior analysis thread started" << std::endl;

    while (!detector->m_shouldStop) {
        try {
            // Analyze behavioral patterns
            // This would integrate with the hooking engine to track API calls

            // Sleep for analysis interval
            Sleep(10000); // 10 seconds
        }
        catch (const std::exception& e) {
            std::cerr << "[ThreatDetector] Exception in behavior analysis thread: " << e.what() << std::endl;
        }
    }

    std::cout << "[ThreatDetector] Behavior analysis thread stopped" << std::endl;
    return 0;
}

std::vector<MemoryScanResult> ThreatDetector::ScanProcess(DWORD processId) {
    std::vector<MemoryScanResult> results;

    try {
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
        if (!hProcess) {
            return results;
        }

        // Enumerate memory regions
        MEMORY_BASIC_INFORMATION mbi;
        uintptr_t address = 0;

        while (VirtualQueryEx(hProcess, reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi))) {
            // Only scan committed, executable memory
            if (mbi.State == MEM_COMMIT &&
                (mbi.Protect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE))) {

                // Limit scan size
                size_t scanSize = std::min(mbi.RegionSize, m_maxScanSize);

                std::vector<uint8_t> data(scanSize);
                SIZE_T bytesRead;
                if (::ReadProcessMemory(hProcess, mbi.BaseAddress, data.data(), scanSize, &bytesRead)) {
                    data.resize(bytesRead);

                    // Scan this region
                    std::vector<MemoryScanResult> regionResults;
                    ScanWithSignatures(data, reinterpret_cast<uintptr_t>(mbi.BaseAddress), regionResults);
                    PerformHeuristicAnalysis(data, reinterpret_cast<uintptr_t>(mbi.BaseAddress), regionResults);

                    results.insert(results.end(), regionResults.begin(), regionResults.end());
                }
            }

            address = reinterpret_cast<uintptr_t>(mbi.BaseAddress) + mbi.RegionSize;

            // Prevent infinite loop
            if (address == 0) break;
        }

        CloseHandle(hProcess);

    }
    catch (const std::exception& e) {
        std::cerr << "[ThreatDetector] Exception in ScanProcess: " << e.what() << std::endl;
    }

    return results;
}
