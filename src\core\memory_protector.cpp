#include "memory_protector.h"
#include <iostream>
#include <algorithm>
#include <psapi.h>
// Detours function declarations
extern "C" {
    LONG DetourTransactionBegin();
    LONG DetourUpdateThread(HANDLE hThread);
    <PERSON>ON<PERSON> DetourAttach(PVOID* ppPointer, PVOID pDetour);
    LONG DetourDetach(PVOID* ppPointer, PVOID pDetour);
    LONG DetourTransactionCommit();
    LONG DetourTransactionAbort();
}

// Static member initialization
MemoryProtector* MemoryProtector::s_instance = nullptr;
LPVOID (WINAPI* MemoryProtector::s_originalVirtualAlloc)(LPVOID, SIZE_T, DWORD, DWORD) = VirtualAlloc;
BOOL (WINAPI* MemoryProtector::s_originalVirtualProtect)(LPVOID, SIZE_T, DWORD, PDWORD) = VirtualProtect;
BOOL (WINAPI* MemoryProtector::s_originalWriteProcessMemory)(HAN<PERSON><PERSON>, LPVOID, LPCVOID, SIZE_T, SIZE_T*) = WriteProcessMemory;

MemoryProtector::MemoryProtector() {
    s_instance = this;
}

MemoryProtector::~MemoryProtector() {
    Stop();
    s_instance = nullptr;
}

bool MemoryProtector::Start() {
    if (m_isRunning) {
        return true;
    }

    try {
        // Install vectored exception handler
        m_exceptionHandler = AddVectoredExceptionHandler(1, VectoredExceptionHandler);
        if (!m_exceptionHandler) {
            std::cerr << "[MemoryProtector] Failed to install exception handler" << std::endl;
            return false;
        }

        // Install API hooks
        DetourTransactionBegin();
        DetourUpdateThread(GetCurrentThread());
        
        if (m_monitorVirtualAlloc) {
            DetourAttach(&(PVOID&)s_originalVirtualAlloc, HookedVirtualAlloc);
        }
        if (m_monitorVirtualProtect) {
            DetourAttach(&(PVOID&)s_originalVirtualProtect, HookedVirtualProtect);
        }
        if (m_monitorWriteProcessMemory) {
            DetourAttach(&(PVOID&)s_originalWriteProcessMemory, HookedWriteProcessMemory);
        }

        LONG result = DetourTransactionCommit();
        if (result != NO_ERROR) {
            std::cerr << "[MemoryProtector] Failed to install hooks: " << result << std::endl;
            if (m_exceptionHandler) {
                RemoveVectoredExceptionHandler(m_exceptionHandler);
                m_exceptionHandler = nullptr;
            }
            return false;
        }

        m_isRunning = true;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[MemoryProtector] Failed to start: " << e.what() << std::endl;
        return false;
    }
}

void MemoryProtector::Stop() {
    if (!m_isRunning) {
        return;
    }

    // Remove hooks
    DetourTransactionBegin();
    DetourUpdateThread(GetCurrentThread());
    
    if (m_monitorVirtualAlloc) {
        DetourDetach(&(PVOID&)s_originalVirtualAlloc, HookedVirtualAlloc);
    }
    if (m_monitorVirtualProtect) {
        DetourDetach(&(PVOID&)s_originalVirtualProtect, HookedVirtualProtect);
    }
    if (m_monitorWriteProcessMemory) {
        DetourDetach(&(PVOID&)s_originalWriteProcessMemory, HookedWriteProcessMemory);
    }
    
    DetourTransactionCommit();

    // Remove exception handler
    if (m_exceptionHandler) {
        RemoveVectoredExceptionHandler(m_exceptionHandler);
        m_exceptionHandler = nullptr;
    }

    m_isRunning = false;
}

bool MemoryProtector::ProtectRegion(uintptr_t address, size_t size, DWORD protection) {
    DWORD oldProtection;
    if (!VirtualProtect((LPVOID)address, size, protection, &oldProtection)) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_regionsMutex);
    MemoryRegion region;
    region.baseAddress = address;
    region.size = size;
    region.originalProtection = oldProtection;
    region.currentProtection = protection;
    region.isProtected = true;
    region.isSuspicious = false;
    GetSystemTime(&region.protectionTime);

    m_protectedRegions[address] = region;
    return true;
}

bool MemoryProtector::UnprotectRegion(uintptr_t address) {
    std::lock_guard<std::mutex> lock(m_regionsMutex);
    auto it = m_protectedRegions.find(address);
    if (it == m_protectedRegions.end()) {
        return false;
    }

    DWORD oldProtection;
    bool success = VirtualProtect((LPVOID)address, it->second.size, 
                                 it->second.originalProtection, &oldProtection);
    
    if (success) {
        m_protectedRegions.erase(it);
    }
    
    return success;
}

bool MemoryProtector::IsRegionProtected(uintptr_t address) const {
    std::lock_guard<std::mutex> lock(m_regionsMutex);
    return m_protectedRegions.find(address) != m_protectedRegions.end();
}

void MemoryProtector::SetCallback(MemoryViolationCallback callback) {
    m_callback = callback;
}

size_t MemoryProtector::GetProtectedRegionsCount() const {
    std::lock_guard<std::mutex> lock(m_regionsMutex);
    return m_protectedRegions.size();
}

std::vector<MemoryRegion> MemoryProtector::GetProtectedRegions() const {
    std::lock_guard<std::mutex> lock(m_regionsMutex);
    std::vector<MemoryRegion> regions;
    for (const auto& pair : m_protectedRegions) {
        regions.push_back(pair.second);
    }
    return regions;
}

std::vector<MemoryViolation> MemoryProtector::GetRecentViolations() const {
    std::lock_guard<std::mutex> lock(m_violationsMutex);
    return m_recentViolations;
}

LONG WINAPI MemoryProtector::VectoredExceptionHandler(PEXCEPTION_POINTERS exceptionInfo) {
    if (s_instance && exceptionInfo->ExceptionRecord->ExceptionCode == EXCEPTION_ACCESS_VIOLATION) {
        s_instance->HandleMemoryViolation(exceptionInfo);
    }
    return EXCEPTION_CONTINUE_SEARCH;
}

void MemoryProtector::HandleMemoryViolation(PEXCEPTION_POINTERS exceptionInfo) {
    MemoryViolation violation;
    violation.address = exceptionInfo->ExceptionRecord->ExceptionInformation[1];
    violation.processId = GetCurrentProcessId();
    violation.threadId = GetCurrentThreadId();
    violation.violationType = (exceptionInfo->ExceptionRecord->ExceptionInformation[0] == 0) ? "Read" : "Write";
    violation.description = "Memory access violation detected";
    GetSystemTime(&violation.timestamp);

    // Store violation
    {
        std::lock_guard<std::mutex> lock(m_violationsMutex);
        m_recentViolations.push_back(violation);
        if (m_recentViolations.size() > MAX_RECENT_VIOLATIONS) {
            m_recentViolations.erase(m_recentViolations.begin());
        }
    }

    m_violationsCount++;

    // Notify callback
    if (m_callback) {
        m_callback(violation);
    }
}

LPVOID WINAPI MemoryProtector::HookedVirtualAlloc(LPVOID lpAddress, SIZE_T dwSize, DWORD flAllocationType, DWORD flProtect) {
    LPVOID result = s_originalVirtualAlloc(lpAddress, dwSize, flAllocationType, flProtect);
    
    if (s_instance && result && s_instance->IsSuspiciousAllocation(result, dwSize, flProtect)) {
        MemoryViolation violation;
        violation.address = (uintptr_t)result;
        violation.processId = GetCurrentProcessId();
        violation.threadId = GetCurrentThreadId();
        violation.violationType = "Suspicious Allocation";
        violation.description = "Potentially malicious memory allocation detected";
        GetSystemTime(&violation.timestamp);

        if (s_instance->m_callback) {
            s_instance->m_callback(violation);
        }
    }
    
    return result;
}

BOOL WINAPI MemoryProtector::HookedVirtualProtect(LPVOID lpAddress, SIZE_T dwSize, DWORD flNewProtect, PDWORD lpflOldProtect) {
    if (s_instance && s_instance->IsSuspiciousProtectionChange(lpAddress, flNewProtect)) {
        MemoryViolation violation;
        violation.address = (uintptr_t)lpAddress;
        violation.processId = GetCurrentProcessId();
        violation.threadId = GetCurrentThreadId();
        violation.violationType = "Suspicious Protection Change";
        violation.description = "Potentially malicious memory protection change";
        GetSystemTime(&violation.timestamp);

        if (s_instance->m_callback) {
            s_instance->m_callback(violation);
        }
    }
    
    return s_originalVirtualProtect(lpAddress, dwSize, flNewProtect, lpflOldProtect);
}

BOOL WINAPI MemoryProtector::HookedWriteProcessMemory(HANDLE hProcess, LPVOID lpBaseAddress, LPCVOID lpBuffer, SIZE_T nSize, SIZE_T* lpNumberOfBytesWritten) {
    if (s_instance && hProcess != GetCurrentProcess()) {
        MemoryViolation violation;
        violation.address = (uintptr_t)lpBaseAddress;
        violation.processId = GetProcessId(hProcess);
        violation.threadId = GetCurrentThreadId();
        violation.violationType = "Cross-Process Write";
        violation.description = "Cross-process memory write detected";
        GetSystemTime(&violation.timestamp);

        if (s_instance->m_callback) {
            s_instance->m_callback(violation);
        }
    }
    
    return s_originalWriteProcessMemory(hProcess, lpBaseAddress, lpBuffer, nSize, lpNumberOfBytesWritten);
}

bool MemoryProtector::IsSuspiciousAllocation(LPVOID address, SIZE_T size, DWORD protection) {
    // Check for executable allocations
    if (protection & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE | PAGE_EXECUTE_WRITECOPY)) {
        return true;
    }
    
    // Check for large allocations
    if (size > 1024 * 1024) { // 1MB
        return true;
    }
    
    return false;
}

bool MemoryProtector::IsSuspiciousProtectionChange(LPVOID address, DWORD newProtection) {
    // Check for making memory executable
    if (newProtection & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE | PAGE_EXECUTE_WRITECOPY)) {
        return true;
    }
    
    return false;
}
