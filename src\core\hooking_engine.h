#pragma once

#include <windows.h>
#include <vector>
#include <string>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <functional>

// Hook types
enum class HookType {
    INLINE_HOOK,
    IAT_HOOK,
    EAT_HOOK,
    DETOUR_HOOK,
    HARD<PERSON>RE_BREAKPOINT,
    PAGE_GUARD_HOOK
};

// Hook status
enum class HookStatus {
    NOT_INSTALLED,
    INSTALLING,
    INSTALLED,
    FAILED,
    REMOVING,
    REMOVED
};

// Hook configuration
struct HookConfig {
    HookType type = HookType::DETOUR_HOOK;
    bool preserveRegisters = true;
    bool enableLogging = true;
    bool enableCallStack = false;
    size_t trampolineSize = 32;
    bool useHotPatching = false;
    bool enableThreadSafety = true;
};

// Hook context information
struct HookContext {
    DWORD processId;
    DWORD threadId;
    uintptr_t returnAddress;
    uintptr_t stackPointer;
    CONTEXT registers;
    std::vector<uintptr_t> callStack;
    SYSTEMTIME timestamp;
    std::string callerModule;
    std::string targetModule;
};

// Hook callback function type
using HookCallback = std::function<bool(const HookContext& context, void** parameters, size_t paramCount)>;

// Hook information structure
struct Hook {
    std::string name;
    std::string moduleName;
    std::string functionName;
    uintptr_t originalAddress;
    uintptr_t hookAddress;
    uintptr_t trampolineAddress;
    std::vector<uint8_t> originalBytes;
    std::vector<uint8_t> hookBytes;
    HookType type;
    HookStatus status;
    HookConfig config;
    HookCallback callback;
    SYSTEMTIME installTime;
    size_t callCount;
    bool isEnabled;
    
    Hook() : originalAddress(0), hookAddress(0), trampolineAddress(0), 
             type(HookType::DETOUR_HOOK), status(HookStatus::NOT_INSTALLED),
             callCount(0), isEnabled(false) {
        GetSystemTime(&installTime);
    }
};

/**
 * @brief Advanced API Hooking Engine
 * 
 * Professional-grade hooking system with multiple hooking techniques,
 * thread safety, and advanced features for malware analysis.
 */
class HookingEngine {
public:
    HookingEngine();
    ~HookingEngine();

    // Initialization
    bool Initialize();
    void Shutdown();
    bool IsInitialized() const { return m_initialized; }

    // Hook management
    bool InstallHook(const std::string& name, const std::string& moduleName, 
                    const std::string& functionName, HookCallback callback,
                    const HookConfig& config = HookConfig{});
    
    bool InstallHook(const std::string& name, uintptr_t targetAddress, 
                    HookCallback callback, const HookConfig& config = HookConfig{});
    
    bool RemoveHook(const std::string& name);
    bool EnableHook(const std::string& name);
    bool DisableHook(const std::string& name);
    
    void RemoveAllHooks();
    void EnableAllHooks();
    void DisableAllHooks();

    // Hook information
    std::vector<std::string> GetHookNames() const;
    std::shared_ptr<Hook> GetHook(const std::string& name) const;
    std::vector<std::shared_ptr<Hook>> GetAllHooks() const;
    std::vector<std::shared_ptr<Hook>> GetHooksByModule(const std::string& moduleName) const;
    std::vector<std::shared_ptr<Hook>> GetHooksByType(HookType type) const;
    std::vector<std::shared_ptr<Hook>> GetActiveHooks() const;

    // Statistics
    struct HookingStatistics {
        size_t totalHooks = 0;
        size_t activeHooks = 0;
        size_t failedHooks = 0;
        size_t totalCalls = 0;
        double averageCallsPerHook = 0.0;
        SYSTEMTIME lastHookTime = {};
        std::unordered_map<HookType, size_t> hooksByType;
        std::unordered_map<std::string, size_t> hooksByModule;
    };
    HookingStatistics GetStatistics() const;

    // Advanced features
    bool SetGlobalHookFilter(std::function<bool(const std::string&, const std::string&)> filter);
    bool EnableCallStackCapture(const std::string& hookName, bool enable = true);
    bool SetHookPriority(const std::string& hookName, int priority);
    
    // Memory protection
    bool ProtectHookMemory(const std::string& hookName, bool protect = true);
    bool ValidateHookIntegrity(const std::string& hookName);
    bool ValidateAllHooksIntegrity();

    // Debugging and analysis
    std::vector<HookContext> GetRecentCalls(const std::string& hookName, size_t maxCount = 100) const;
    std::string GetHookDisassembly(const std::string& hookName) const;
    bool DumpHookMemory(const std::string& hookName, const std::string& filePath) const;

    // Utility functions
    static std::string HookTypeToString(HookType type);
    static std::string HookStatusToString(HookStatus status);
    static uintptr_t GetModuleBaseAddress(const std::string& moduleName);
    static uintptr_t GetFunctionAddress(const std::string& moduleName, const std::string& functionName);
    static std::vector<uint8_t> ReadMemory(uintptr_t address, size_t size);
    static bool WriteMemory(uintptr_t address, const std::vector<uint8_t>& data);

private:
    bool m_initialized;
    mutable std::mutex m_hooksMutex;
    std::unordered_map<std::string, std::shared_ptr<Hook>> m_hooks;
    
    // Global settings
    std::function<bool(const std::string&, const std::string&)> m_globalFilter;
    bool m_enableGlobalLogging;
    bool m_enableGlobalCallStack;
    
    // Statistics
    mutable std::mutex m_statsMutex;
    HookingStatistics m_statistics;
    
    // Memory management
    std::vector<void*> m_allocatedMemory;
    mutable std::mutex m_memoryMutex;
    
    // Internal methods
    bool InstallInlineHook(std::shared_ptr<Hook> hook);
    bool InstallIATHook(std::shared_ptr<Hook> hook);
    bool InstallEATHook(std::shared_ptr<Hook> hook);
    bool InstallDetourHook(std::shared_ptr<Hook> hook);
    bool InstallHardwareBreakpoint(std::shared_ptr<Hook> hook);
    bool InstallPageGuardHook(std::shared_ptr<Hook> hook);
    
    bool RemoveInlineHook(std::shared_ptr<Hook> hook);
    bool RemoveIATHook(std::shared_ptr<Hook> hook);
    bool RemoveEATHook(std::shared_ptr<Hook> hook);
    bool RemoveDetourHook(std::shared_ptr<Hook> hook);
    bool RemoveHardwareBreakpoint(std::shared_ptr<Hook> hook);
    bool RemovePageGuardHook(std::shared_ptr<Hook> hook);
    
    void* AllocateExecutableMemory(size_t size);
    void FreeExecutableMemory(void* memory);
    
    bool CreateTrampoline(std::shared_ptr<Hook> hook);
    bool WriteJumpInstruction(uintptr_t from, uintptr_t to);
    bool WriteCallInstruction(uintptr_t from, uintptr_t to);
    
    std::vector<uintptr_t> CaptureCallStack(CONTEXT* context = nullptr);
    std::string GetModuleNameFromAddress(uintptr_t address);
    
    void UpdateStatistics();
    void OnHookCalled(std::shared_ptr<Hook> hook, const HookContext& context);
    
    // Exception handling
    static LONG WINAPI VectoredExceptionHandler(PEXCEPTION_POINTERS exceptionInfo);
    void* m_exceptionHandler;
    
    // Thread safety
    std::mutex m_installMutex;
    std::atomic<bool> m_isInstalling{false};
    
    // Hook validation
    bool ValidateHookParameters(const std::string& name, uintptr_t targetAddress, 
                               HookCallback callback, const HookConfig& config);
    bool IsAddressHookable(uintptr_t address);
    bool IsMemoryExecutable(uintptr_t address);
    bool IsMemoryWritable(uintptr_t address);
    
    // Disassembly support
    std::string DisassembleInstruction(uintptr_t address);
    size_t GetInstructionLength(uintptr_t address);
    bool IsRelativeInstruction(uintptr_t address);
    
    // Anti-detection
    bool ObfuscateHook(std::shared_ptr<Hook> hook);
    bool RandomizeTrampoline(std::shared_ptr<Hook> hook);
    bool UsePolymorphicCode(std::shared_ptr<Hook> hook);
};
