[cmake]
version = "3.15"
cmkr-include = "cmake/cmkr.cmake"

[project]
name = "AdvancedProtectionPlugin"
description = "Advanced Protection Plugin v2.0 for x64dbg - Professional Security System"
languages = ["CXX"]
include-before = [
	"cmake/msvc-static-runtime.cmake",
	"cmake/msvc-configurations.cmake",
]

[fetch-content.x64dbg]
url = "https://sourceforge.net/projects/x64dbg/files/snapshots/snapshot_2023-06-10_18-05.zip"
sha1 = "04468bd61fb36d6b10d17f342f03ef12f5b2ce62"
include-after = ["cmake/x64dbg.cmake"]



[template.plugin]
type = "shared"
add-function = "x64dbg_plugin"

[target.AdvancedProtectionPlugin]
type = "plugin"
sources = [
    "src/pluginmain.cpp",
    "src/plugin.cpp"
]
include-directories = [
    "src"
]
compile-definitions = [
    "UNICODE",
    "_UNICODE",
    "WIN32_LEAN_AND_MEAN",
    "_CRT_SECURE_NO_WARNINGS"
]
link-libraries = [
    "user32",
    "kernel32"
]