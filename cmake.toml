[cmake]
version = "3.15"
cmkr-include = "cmake/cmkr.cmake"

[project]
name = "AdvancedProtectionPlugin"
description = "Advanced Protection Plugin v2.0 for x64dbg - Professional Security System"
languages = ["CXX"]
include-before = [
	"cmake/msvc-static-runtime.cmake",
	"cmake/msvc-configurations.cmake",
]

[fetch-content.x64dbg]
url = "https://sourceforge.net/projects/x64dbg/files/snapshots/snapshot_2023-06-10_18-05.zip"
sha1 = "04468bd61fb36d6b10d17f342f03ef12f5b2ce62"
include-after = ["cmake/x64dbg.cmake"]

[fetch-content.imgui]
git = "https://github.com/ocornut/imgui.git"
tag = "v1.89.9"

[fetch-content.detours]
git = "https://github.com/microsoft/Detours.git"
tag = "v4.0.1"

[fetch-content.capstone]
git = "https://github.com/capstone-engine/capstone.git"
tag = "5.0.1"

[template.plugin]
type = "shared"
add-function = "x64dbg_plugin"

[target.AdvancedProtectionPlugin]
type = "plugin"
sources = [
    "src/plugin.cpp",
    "src/core/protection_engine.cpp",
    "src/core/protection_engine.h",
    "src/core/hooking_engine.cpp",
    "src/core/hooking_engine.h",
    "src/core/threat_detector.cpp",
    "src/core/threat_detector.h",
    "src/core/network_monitor.cpp",
    "src/core/network_monitor.h",
    "src/ui/protection_ui.cpp",
    "src/ui/protection_ui.h"
]
include-directories = [
    "src",
    "${imgui_SOURCE_DIR}",
    "${imgui_SOURCE_DIR}/backends",
    "${detours_SOURCE_DIR}/include",
    "${capstone_SOURCE_DIR}/include"
]
compile-definitions = [
    "UNICODE",
    "_UNICODE",
    "WIN32_LEAN_AND_MEAN",
    "NOMINMAX",
    "IMGUI_IMPL_WIN32_DISABLE_GAMEPAD",
    "_CRT_SECURE_NO_WARNINGS"
]
link-libraries = [
    "user32",
    "kernel32",
    "gdi32",
    "shell32",
    "ole32",
    "oleaut32",
    "uuid",
    "comdlg32",
    "advapi32",
    "d3d11",
    "dxgi",
    "ws2_32",
    "psapi"
]